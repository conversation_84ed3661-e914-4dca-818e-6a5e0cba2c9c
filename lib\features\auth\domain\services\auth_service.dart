import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import '../../../../core/models/user.dart';

class AuthService {
  User? _currentUser;
  String? _sessionToken;
  final Dio _dio = Dio();
  
  // Backend API URL - Update this to match your deployment
  static const String baseUrl = 'http://pricedropalert.mobunite.com/api_simple.php';

  Stream<User?> get authStateChanges async* {
    // Load user from local storage on app start
    final user = await _loadUserLocally();
    if (user != null) {
      _currentUser = user;
      _sessionToken = await _loadSessionToken();
    }
    yield _currentUser;
  }

  User? get currentUser => _currentUser;

  Future<User?> signInWithEmailAndPassword(String email, String password) async {
    try {
      debugPrint('🔐 Attempting login for: $email');
      
      final response = await _dio.post(
        '$baseUrl?path=auth/login',
        data: {
          'email': email,
          'password': password,
        },
        options: Options(
          headers: {'Content-Type': 'application/json'},
          validateStatus: (status) => status! < 500,
        ),
      );
      
      debugPrint('📡 Login response: ${response.statusCode} - ${response.data}');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final userData = response.data['user'];
        _sessionToken = response.data['session_token'];
        
        final user = User(
          id: userData['id'].toString(),
          email: userData['email'],
          displayName: userData['display_name'],
          photoUrl: userData['photo_url'],
          createdAt: DateTime.parse(userData['created_at'] ?? DateTime.now().toIso8601String()),
          lastLoginAt: DateTime.now(),
          preferences: UserPreferences(),
        );
        
        await _saveUserLocally(user);
        await _saveSessionToken(_sessionToken!);
        _currentUser = user;
        
        debugPrint('✅ Login successful for user: ${user.email}');
        return user;
      } else {
        final errorMessage = response.data['error'] ?? response.data['message'] ?? 'Login failed';
        debugPrint('❌ Login failed: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('💥 Login error: $e');
      if (e is DioException) {
        if (e.response?.data != null) {
          final errorMsg = e.response!.data['error'] ?? e.response!.data['message'] ?? 'Network error';
          throw Exception(errorMsg);
        }
        throw Exception('Network error: ${e.message}');
      }
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  Future<User?> createUserWithEmailAndPassword(String email, String password, String displayName) async {
    try {
      debugPrint('📝 Attempting registration for: $email');
      
      final response = await _dio.post(
        '$baseUrl?path=auth/register',
        data: {
          'email': email,
          'password': password,
          'display_name': displayName,
        },
        options: Options(
          headers: {'Content-Type': 'application/json'},
          validateStatus: (status) => status! < 500,
        ),
      );
      
      debugPrint('📡 Registration response: ${response.statusCode} - ${response.data}');
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        debugPrint('✅ Registration successful, attempting auto-login...');
        // Auto-login after successful registration
        return await signInWithEmailAndPassword(email, password);
      } else {
        final errorMessage = response.data['error'] ?? response.data['message'] ?? 'Registration failed';
        debugPrint('❌ Registration failed: $errorMessage');
        
        // Handle detailed error messages for password validation
        if (response.data['details'] != null) {
          final details = response.data['details'] as List;
          throw Exception('$errorMessage: ${details.join(', ')}');
        }
        
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('💥 Registration error: $e');
      if (e is DioException) {
        if (e.response?.data != null) {
          final errorMsg = e.response!.data['error'] ?? e.response!.data['message'] ?? 'Network error';
          throw Exception(errorMsg);
        }
        throw Exception('Network error: ${e.message}');
      }
      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  Future<void> signOut() async {
    try {
      if (_sessionToken != null) {
        // Call logout endpoint to invalidate session
        await _dio.post(
          '$baseUrl?path=auth/logout',
          options: Options(
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $_sessionToken',
            },
          ),
        );
      }
    } catch (e) {
      debugPrint('⚠️ Logout API call failed: $e');
    } finally {
      // Clear local data regardless of API call result
      _currentUser = null;
      _sessionToken = null;
      await _clearUserLocally();
      debugPrint('👋 User signed out');
    }
  }

  Future<void> _saveUserLocally(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_id', user.id);
    await prefs.setString('user_email', user.email);
    await prefs.setString('user_display_name', user.displayName ?? '');
    await prefs.setString('user_photo_url', user.photoUrl ?? '');
    debugPrint('💾 User saved locally: ${user.email}');
  }

  Future<User?> _loadUserLocally() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      final userEmail = prefs.getString('user_email');
      final userDisplayName = prefs.getString('user_display_name');
      final userPhotoUrl = prefs.getString('user_photo_url');
      
      if (userId != null && userEmail != null) {
        final user = User(
          id: userId,
          email: userEmail,
          displayName: userDisplayName?.isEmpty == true ? null : userDisplayName,
          photoUrl: userPhotoUrl?.isEmpty == true ? null : userPhotoUrl,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          preferences: UserPreferences(),
        );
        debugPrint('📱 User loaded from local storage: ${user.email}');
        return user;
      }
    } catch (e) {
      debugPrint('⚠️ Error loading user locally: $e');
    }
    return null;
  }

  Future<void> _saveSessionToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('session_token', token);
    debugPrint('🔑 Session token saved');
  }

  Future<String?> _loadSessionToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('session_token');
  }

  Future<void> _clearUserLocally() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_id');
    await prefs.remove('user_email');
    await prefs.remove('user_display_name');
    await prefs.remove('user_photo_url');
    await prefs.remove('session_token');
    debugPrint('🗑️ Local user data cleared');
  }

  // Get current session token for API calls
  String? get sessionToken => _sessionToken;
}
