import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:hive_flutter/hive_flutter.dart';


import 'core/app.dart';
import 'core/models/product.dart';
import 'core/models/user.dart';

import 'core/services/api_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/sharing_service.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize services
  
  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(ProductAdapter());
  Hive.registerAdapter(PriceHistoryAdapter());
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(UserPreferencesAdapter());

  // Initialize services
  ApiService.instance.initialize();
  await NotificationService.initialize();
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ),
  );
  
  // Handle sharing intent
  SharingService.initialize();
  
  runApp(
    const ProviderScope(
      child: PriceDropApp(),
    ),
  );
}
