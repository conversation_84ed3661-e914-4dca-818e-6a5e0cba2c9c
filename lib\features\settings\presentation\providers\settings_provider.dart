import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive/hive.dart';
import '../../../../core/models/user.dart';

class SettingsNotifier extends StateNotifier<UserPreferences> {
  late Box<UserPreferences> _settingsBox;
  bool _isInitialized = false;

  SettingsNotifier() : super(UserPreferences()) {
    _initializeSettings();
  }

  Future<void> _initializeSettings() async {
    try {
      _settingsBox = await Hive.openBox<UserPreferences>('settings');
      final savedSettings = _settingsBox.get('user_preferences');
      if (savedSettings != null) {
        state = savedSettings;
      }
      _isInitialized = true;
    } catch (e) {
      // If initialization fails, continue with default settings
      _isInitialized = true;
    }
  }

  Future<void> _saveSettings() async {
    if (!_isInitialized) {
      await _initializeSettings();
    }
    try {
      await _settingsBox.put('user_preferences', state);
    } catch (e) {
      // Handle save errors gracefully
    }
  }

  void updateNotifications(bool enabled) {
    state = state.copyWith(notificationsEnabled: enabled);
    _saveSettings();
  }

  void updateCheckFrequency(int hours) {
    state = state.copyWith(checkFrequencyHours: hours);
    _saveSettings();
  }

  void updateDarkMode(bool enabled) {
    state = state.copyWith(darkMode: enabled);
    _saveSettings();
  }

  void updateCurrency(String currency) {
    state = state.copyWith(currency: currency);
    _saveSettings();
  }

  void togglePlatform(String platform) {
    final platforms = List<String>.from(state.preferredPlatforms);
    if (platforms.contains(platform)) {
      platforms.remove(platform);
    } else {
      platforms.add(platform);
    }

    state = state.copyWith(preferredPlatforms: platforms);
    _saveSettings();
  }

  Future<void> clearCache() async {
    try {
      // Clear all Hive boxes except settings
      final boxNames = ['products', 'price_history', 'notifications'];
      for (final boxName in boxNames) {
        if (Hive.isBoxOpen(boxName)) {
          final box = Hive.box(boxName);
          await box.clear();
        }
      }
    } catch (e) {
      // Handle errors gracefully
      rethrow;
    }
  }

  Future<void> deleteAllData() async {
    try {
      // Clear all Hive boxes including settings
      final boxNames = ['products', 'price_history', 'notifications', 'settings'];
      for (final boxName in boxNames) {
        if (Hive.isBoxOpen(boxName)) {
          final box = Hive.box(boxName);
          await box.clear();
        }
      }

      // Reset to default settings
      state = UserPreferences();
    } catch (e) {
      // Handle errors gracefully
      rethrow;
    }
  }
}

final settingsProvider = StateNotifierProvider<SettingsNotifier, UserPreferences>((ref) {
  return SettingsNotifier();
});

final themeModeProvider = Provider<ThemeMode>((ref) {
  final settings = ref.watch(settingsProvider);
  return settings.darkMode ? ThemeMode.dark : ThemeMode.light;
});