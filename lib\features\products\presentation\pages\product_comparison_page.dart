import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/models/product.dart';

class ProductComparisonPage extends ConsumerStatefulWidget {
  final Product product;

  const ProductComparisonPage({
    super.key,
    required this.product,
  });

  @override
  ConsumerState<ProductComparisonPage> createState() => _ProductComparisonPageState();
}

class _ProductComparisonPageState extends ConsumerState<ProductComparisonPage> {
  bool _isLoading = true;
  List<ComparisonProduct> _comparisons = [];

  @override
  void initState() {
    super.initState();
    _loadComparisons();
  }

  Future<void> _loadComparisons() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate API call to get product comparisons
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock comparison data
      _comparisons = [
        ComparisonProduct(
          title: widget.product.title,
          platform: 'Amazon',
          price: 25999,
          originalPrice: 29999,
          imageUrl: 'https://via.placeholder.com/150',
          url: 'https://amazon.in/product',
          rating: 4.5,
          reviews: 1250,
          availability: 'In Stock',
          delivery: 'Free delivery by tomorrow',
        ),
        ComparisonProduct(
          title: widget.product.title,
          platform: 'Flipkart',
          price: 26499,
          originalPrice: 30999,
          imageUrl: 'https://via.placeholder.com/150',
          url: 'https://flipkart.com/product',
          rating: 4.3,
          reviews: 890,
          availability: 'In Stock',
          delivery: 'Free delivery in 2 days',
        ),
        ComparisonProduct(
          title: widget.product.title,
          platform: 'Myntra',
          price: 27999,
          originalPrice: 31999,
          imageUrl: 'https://via.placeholder.com/150',
          url: 'https://myntra.com/product',
          rating: 4.2,
          reviews: 567,
          availability: 'Limited Stock',
          delivery: 'Free delivery in 3-4 days',
        ),
      ];
    } catch (e) {
      // Handle error
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Compare Prices'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadComparisons,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildComparisonList(),
    );
  }

  Widget _buildComparisonList() {
    if (_comparisons.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No comparisons found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This product is not available on other platforms',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // Sort by price (lowest first)
    _comparisons.sort((a, b) => a.price.compareTo(b.price));

    return Column(
      children: [
        // Best Deal Banner
        if (_comparisons.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.star, color: Colors.white),
                    SizedBox(width: 8),
                    Text(
                      'Best Deal',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${_comparisons.first.platform} offers the lowest price',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '₹${NumberFormat('#,##0').format(_comparisons.first.price)}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

        // Comparison List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _comparisons.length,
            itemBuilder: (context, index) {
              final comparison = _comparisons[index];
              final isLowestPrice = index == 0;
              
              return _buildComparisonCard(comparison, isLowestPrice);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildComparisonCard(ComparisonProduct comparison, bool isLowestPrice) {
    final discountPercentage = ((comparison.originalPrice - comparison.price) / comparison.originalPrice * 100);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Platform and Badge
            Row(
              children: [
                _buildPlatformLogo(comparison.platform),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    comparison.platform,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (isLowestPrice)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'BEST PRICE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: CachedNetworkImage(
                    imageUrl: comparison.imageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey.shade200,
                      child: const Icon(Icons.image),
                    ),
                    errorWidget: (context, url, error) => Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey.shade200,
                      child: const Icon(Icons.broken_image),
                    ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Product Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Price
                      Row(
                        children: [
                          Text(
                            '₹${NumberFormat('#,##0').format(comparison.price)}',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (comparison.originalPrice > comparison.price)
                            Text(
                              '₹${NumberFormat('#,##0').format(comparison.originalPrice)}',
                              style: const TextStyle(
                                fontSize: 14,
                                decoration: TextDecoration.lineThrough,
                                color: Colors.grey,
                              ),
                            ),
                        ],
                      ),
                      
                      if (discountPercentage > 0)
                        Text(
                          '${discountPercentage.toStringAsFixed(1)}% off',
                          style: const TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      
                      const SizedBox(height: 8),
                      
                      // Rating and Reviews
                      Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            comparison.rating.toString(),
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '(${NumberFormat('#,##0').format(comparison.reviews)})',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Availability
                      Text(
                        comparison.availability,
                        style: TextStyle(
                          color: comparison.availability == 'In Stock' 
                              ? Colors.green 
                              : Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      
                      // Delivery
                      Text(
                        comparison.delivery,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _viewProduct(comparison.url),
                    child: const Text('View Product'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _buyNow(comparison.url),
                    child: const Text('Buy Now'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlatformLogo(String platform) {
    IconData icon;
    Color color;
    
    switch (platform.toLowerCase()) {
      case 'amazon':
        icon = Icons.shopping_cart;
        color = Colors.orange;
        break;
      case 'flipkart':
        icon = Icons.shopping_bag;
        color = Colors.blue;
        break;
      case 'myntra':
        icon = Icons.checkroom;
        color = Colors.pink;
        break;
      default:
        icon = Icons.store;
        color = Colors.grey;
    }
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }

  Future<void> _viewProduct(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  Future<void> _buyNow(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}

class ComparisonProduct {
  final String title;
  final String platform;
  final double price;
  final double originalPrice;
  final String imageUrl;
  final String url;
  final double rating;
  final int reviews;
  final String availability;
  final String delivery;

  ComparisonProduct({
    required this.title,
    required this.platform,
    required this.price,
    required this.originalPrice,
    required this.imageUrl,
    required this.url,
    required this.rating,
    required this.reviews,
    required this.availability,
    required this.delivery,
  });
}
