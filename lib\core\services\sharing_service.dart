import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SharingService {
  static StreamSubscription? _intentDataStreamSubscription;
  static String? _sharedUrl;

  static void initialize() {
    // Listen for shared text while app is running
    _intentDataStreamSubscription = ReceiveSharingIntent.instance.getMediaStream().listen(
      (List<SharedMediaFile> files) {
        // Handle shared files if needed
        for (final file in files) {
          if (file.type == SharedMediaType.text && file.path.isNotEmpty) {
            if (_isValidProductUrl(file.path)) {
              _sharedUrl = file.path;
            }
          }
        }
      },
      onError: (err) {
        // Log error - in production use proper logging
        debugPrint('Error receiving shared media: $err');
      },
    );

    // Get initial shared media when app is opened via share
    ReceiveSharingIntent.instance.getInitialMedia().then((List<SharedMediaFile> files) {
      for (final file in files) {
        if (file.type == SharedMediaType.text && file.path.isNotEmpty) {
          if (_isValidProductUrl(file.path)) {
            _sharedUrl = file.path;
          }
        }
      }
    });
  }

  static String? getSharedUrl() {
    final url = _sharedUrl;
    _sharedUrl = null; // Clear after reading
    return url;
  }

  static bool _isValidProductUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;

    final supportedDomains = [
      'amazon.in',
      'amazon.com',
      'flipkart.com',
      'myntra.com',
      'ajio.com',
      'nykaa.com',
    ];

    return supportedDomains.any((domain) => uri.host.contains(domain));
  }

  static void dispose() {
    _intentDataStreamSubscription?.cancel();
  }
}

final sharingServiceProvider = Provider<SharingService>((ref) {
  return SharingService();
});