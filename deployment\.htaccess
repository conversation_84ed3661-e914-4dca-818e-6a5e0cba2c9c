# LiteSpeed Optimized Configuration for PriceDrop Alert
# Enterprise Security & Performance

# Security Headers
<IfModule mod_headers.c>
    # Prevent XSS attacks
    Header always set X-XSS-Protection "1; mode=block"
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header always set X-Frame-Options "DENY"
    
    # Strict Transport Security (HTTPS only)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Remove server information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide sensitive files
<FilesMatch "\.(log|sql|md|json|lock|env)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect configuration files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# Protect backend directory from direct access
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block access to backend files except through API
    RewriteRule ^backend/ - [F,L]
    
    # Block access to sensitive directories
    RewriteRule ^(logs|backups|temp)/ - [F,L]
    
    # API routing
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^api/(.*)$ api_simple.php?path=$1 [QSA,L]
</IfModule>

# Rate Limiting (LiteSpeed specific)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# PHP Security Settings
<IfModule mod_php.c>
    # Disable dangerous functions
    php_admin_value disable_functions "exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source"
    
    # Hide PHP version
    php_flag expose_php Off
    
    # Disable file uploads in API
    php_flag file_uploads Off
    
    # Limit memory and execution time
    php_value memory_limit 128M
    php_value max_execution_time 30
    php_value max_input_time 30
    
    # Session security
    php_flag session.cookie_httponly On
    php_flag session.cookie_secure On
    php_value session.cookie_samesite Strict
</IfModule>

# Block common attack patterns
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block SQL injection attempts
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^e]*e)+mbed.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^o]*o)+bject.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (union|select|insert|drop|delete|update|cast|create|char|convert|alter|declare|or|and|1=1|1=0) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # Block user agent attacks
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # Block suspicious requests
    RewriteCond %{REQUEST_METHOD} ^(HEAD|TRACE|DELETE|TRACK) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# Error pages
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html

# Disable server signature
ServerSignature Off
