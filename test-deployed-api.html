<!DOCTYPE html>
<html>
<head>
    <title>PriceDrop Alert API Validation</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .result { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .status { font-weight: bold; }
    </style>
</head>
<body>
    <h1>🚀 PriceDrop Alert API Validation</h1>
    
    <div class="test-section">
        <h2>1. API Status Test</h2>
        <button onclick="testStatus()">Test API Status</button>
        <div id="statusResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. User Registration Test</h2>
        <button onclick="testRegistration()">Test User Registration</button>
        <div id="registrationResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. User Login Test</h2>
        <button onclick="testLogin()">Test User Login</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Product Scraping Test</h2>
        <button onclick="testScraping()">Test Product Scraping</button>
        <div id="scrapingResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Products API Test</h2>
        <button onclick="testProducts()">Test Get Products</button>
        <div id="productsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>6. User Statistics Test</h2>
        <button onclick="testStatistics()">Test User Statistics</button>
        <div id="statisticsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>7. FCM Token Test</h2>
        <button onclick="testFCMToken()">Test FCM Token Registration</button>
        <div id="fcmResult" class="result"></div>
    </div>

    <script>
        let testUser = null;
        
        async function makeRequest(url, method = 'GET', body = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                const response = await fetch(url, options);
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = { error: 'Invalid JSON response', rawResponse: text };
                }
                
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data,
                    rawText: text
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    data: { error: error.message },
                    rawText: error.message
                };
            }
        }
        
        function displayResult(elementId, result, testName) {
            const element = document.getElementById(elementId);
            const success = result.ok && result.data.success !== false;
            
            element.innerHTML = `
                <div class="status ${success ? 'success' : 'error'}">
                    ${success ? '✅ SUCCESS' : '❌ FAILED'} - ${testName}
                </div>
                <strong>Status:</strong> ${result.status}
                <strong>Response:</strong>
                ${JSON.stringify(result.data, null, 2)}
            `;
        }
        
        async function testStatus() {
            const result = await makeRequest('/api/status');
            displayResult('statusResult', result, 'API Status Check');
        }
        
        async function testRegistration() {
            const timestamp = Date.now();
            const userData = {
                email: `testuser${timestamp}@example.com`,
                password: 'test123',
                name: 'Test User'
            };
            
            const result = await makeRequest('/api/auth/register', 'POST', userData);
            displayResult('registrationResult', result, 'User Registration');
            
            if (result.ok && result.data.success) {
                testUser = userData;
            }
        }
        
        async function testLogin() {
            if (!testUser) {
                document.getElementById('loginResult').innerHTML = '⚠️ Please run registration test first';
                return;
            }
            
            const loginData = {
                email: testUser.email,
                password: testUser.password
            };
            
            const result = await makeRequest('/api/auth/login', 'POST', loginData);
            displayResult('loginResult', result, 'User Login');
        }
        
        async function testScraping() {
            const scrapingData = {
                url: 'https://www.amazon.in/dp/B08N5WRWNW'
            };
            
            const result = await makeRequest('/api/products/scrape', 'POST', scrapingData);
            displayResult('scrapingResult', result, 'Product Scraping');
        }
        
        async function testProducts() {
            const result = await makeRequest('/api/products');
            displayResult('productsResult', result, 'Get Products');
        }
        
        async function testStatistics() {
            const result = await makeRequest('/api/user/statistics');
            displayResult('statisticsResult', result, 'User Statistics');
        }
        
        async function testFCMToken() {
            const tokenData = {
                token: 'test-fcm-token-' + Date.now()
            };
            
            const result = await makeRequest('/api/notifications/register-token', 'POST', tokenData);
            displayResult('fcmResult', result, 'FCM Token Registration');
        }
        
        // Auto-run status test on page load
        window.onload = function() {
            testStatus();
        };
    </script>
</body>
</html>
