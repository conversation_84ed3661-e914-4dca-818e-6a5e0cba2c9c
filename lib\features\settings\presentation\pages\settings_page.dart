import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/settings_provider.dart';
import '../../../products/presentation/providers/product_provider.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settings = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Notifications Section
          const Text(
            'Notifications',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Price Drop Alerts'),
                  subtitle: const Text('Get notified when prices drop'),
                  value: settings.notificationsEnabled,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).updateNotifications(value);
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Check Frequency'),
                  subtitle: Text('Every ${settings.checkFrequencyHours} hours'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showFrequencyDialog(context, ref),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Appearance Section
          const Text(
            'Appearance',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Dark Mode'),
                  subtitle: const Text('Use dark theme'),
                  value: settings.darkMode,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).updateDarkMode(value);
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Currency'),
                  subtitle: Text(settings.currency),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showCurrencyDialog(context, ref),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Platforms Section
          const Text(
            'Preferred Platforms',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Column(
              children: [
                CheckboxListTile(
                  title: const Text('Amazon'),
                  value: settings.preferredPlatforms.contains('Amazon'),
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).togglePlatform('Amazon');
                  },
                ),
                CheckboxListTile(
                  title: const Text('Flipkart'),
                  value: settings.preferredPlatforms.contains('Flipkart'),
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).togglePlatform('Flipkart');
                  },
                ),
                CheckboxListTile(
                  title: const Text('Myntra'),
                  value: settings.preferredPlatforms.contains('Myntra'),
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).togglePlatform('Myntra');
                  },
                ),
                CheckboxListTile(
                  title: const Text('Ajio'),
                  value: settings.preferredPlatforms.contains('Ajio'),
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).togglePlatform('Ajio');
                  },
                ),
                CheckboxListTile(
                  title: const Text('Nykaa'),
                  value: settings.preferredPlatforms.contains('Nykaa'),
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).togglePlatform('Nykaa');
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Data & Storage Section
          const Text(
            'Data & Storage',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Column(
              children: [
                ListTile(
                  title: const Text('Clear Cache'),
                  subtitle: const Text('Clear app cache and temporary files'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _clearCache(context, ref),
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Export Data'),
                  subtitle: const Text('Export your tracked products'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _exportData(context, ref),
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Delete All Data'),
                  subtitle: const Text('Permanently delete all your data'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _deleteAllData(context, ref),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  void _showFrequencyDialog(BuildContext context, WidgetRef ref) {
    final frequencies = [1, 6, 12, 24, 48];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Check Frequency'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: frequencies.map((hours) {
            return RadioListTile<int>(
              title: Text('Every $hours ${hours == 1 ? 'hour' : 'hours'}'),
              value: hours,
              groupValue: ref.read(settingsProvider).checkFrequencyHours,
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateCheckFrequency(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showCurrencyDialog(BuildContext context, WidgetRef ref) {
    final currencies = ['INR', 'USD', 'EUR', 'GBP'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: currencies.map((currency) {
            return RadioListTile<String>(
              title: Text(currency),
              value: currency,
              groupValue: ref.read(settingsProvider).currency,
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateCurrency(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _clearCache(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('This will clear all cached data. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(settingsProvider.notifier).clearCache();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Cache cleared successfully')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Failed to clear cache')),
                  );
                }
              }
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _exportData(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text('This will export all your tracked products to a JSON file.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                // Get all products from the provider
                final productsAsync = ref.read(productsProvider);
                await productsAsync.when(
                  data: (products) async {
                    // For now, just show a success message
                    // In a real implementation, you would use file_picker or share_plus
                    // to save the JSON data to a file
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Found ${products.length} products to export')),
                      );
                    }
                  },
                  loading: () {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Loading products...')),
                      );
                    }
                  },
                  error: (error, stack) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Failed to load products')),
                      );
                    }
                  },
                );
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Export failed')),
                  );
                }
              }
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _deleteAllData(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Data'),
        content: const Text(
          'This will permanently delete all your tracked products and settings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(settingsProvider.notifier).deleteAllData();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('All data deleted successfully')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Failed to delete data')),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}