<?php
/**
 * Security Helper Class
 */

class Security {
    
    /**
     * Check rate limiting for login attempts
     */
    public static function checkLoginRateLimit($ip, $email = null) {
        try {
            $db = Database::getInstance();
            
            // Check IP-based rate limiting (max 5 attempts per 15 minutes)
            $stmt = $db->prepare("
                SELECT COUNT(*) as attempts 
                FROM login_attempts 
                WHERE ip_address = ? 
                AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
            ");
            $stmt->execute([$ip]);
            $ipAttempts = $stmt->fetch()['attempts'];
            
            if ($ipAttempts >= 5) {
                return [
                    'allowed' => false,
                    'message' => 'Too many login attempts from this IP. Please try again in 15 minutes.'
                ];
            }
            
            // Check email-based rate limiting if email provided
            if ($email) {
                $stmt = $db->prepare("
                    SELECT COUNT(*) as attempts 
                    FROM login_attempts 
                    WHERE email = ? 
                    AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
                ");
                $stmt->execute([$email]);
                $emailAttempts = $stmt->fetch()['attempts'];
                
                if ($emailAttempts >= 3) {
                    return [
                        'allowed' => false,
                        'message' => 'Too many login attempts for this email. Please try again in 15 minutes.'
                    ];
                }
            }
            
            return ['allowed' => true];
            
        } catch (Exception $e) {
            error_log("Rate limit check error: " . $e->getMessage());
            return ['allowed' => true]; // Allow on error
        }
    }
    
    /**
     * Log a login attempt
     */
    public static function logLoginAttempt($ip, $email, $success) {
        try {
            $db = Database::getInstance();
            
            $stmt = $db->prepare("
                INSERT INTO login_attempts (ip_address, email, success, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$ip, $email, $success ? 1 : 0]);
            
            // Clean up old attempts
            $db->exec("DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
            
        } catch (Exception $e) {
            error_log("Login attempt logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Validate password strength
     */
    public static function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Sanitize input to prevent XSS
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Get client IP address
     */
    public static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Generate secure session token
     */
    public static function generateSessionToken() {
        return bin2hex(random_bytes(32));
    }
}
?>
