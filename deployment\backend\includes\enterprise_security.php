<?php
/**
 * Enterprise-Level Security Framework
 * Protects against all major attack vectors
 */

class EnterpriseSecurity {
    
    private static $rateLimitCache = [];
    private static $sessionCache = [];
    
    /**
     * SQL Injection Protection - Prepared Statements Only
     */
    public static function validateQuery($query) {
        // Block any direct SQL execution
        $dangerousPatterns = [
            '/\b(DROP|DELETE|TRUNCATE|ALTER|CREATE|INSERT|UPDATE)\b/i',
            '/\b(UNION|SELECT.*FROM)\b/i',
            '/[\'";]/',
            '/--/',
            '/\/\*.*\*\//',
            '/\bOR\s+\d+\s*=\s*\d+/i',
            '/\bAND\s+\d+\s*=\s*\d+/i'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $query)) {
                self::logSecurityEvent('SQL_INJECTION_ATTEMPT', $query);
                throw new SecurityException('Invalid query detected');
            }
        }
        return true;
    }
    
    /**
     * Script Execution Protection
     */
    public static function sanitizeInput($input, $allowHtml = false) {
        if (is_array($input)) {
            return array_map(function($item) use ($allowHtml) {
                return self::sanitizeInput($item, $allowHtml);
            }, $input);
        }
        
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Remove script tags and dangerous content
        $input = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $input);
        $input = preg_replace('/javascript:/i', '', $input);
        $input = preg_replace('/on\w+\s*=/i', '', $input);
        
        if (!$allowHtml) {
            $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        }
        
        return trim($input);
    }
    
    /**
     * Advanced Rate Limiting with Progressive Delays
     */
    public static function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 900) {
        $now = time();
        $key = md5($identifier);
        
        // Clean old entries
        if (isset(self::$rateLimitCache[$key])) {
            self::$rateLimitCache[$key] = array_filter(
                self::$rateLimitCache[$key],
                function($timestamp) use ($now, $timeWindow) {
                    return ($now - $timestamp) < $timeWindow;
                }
            );
        } else {
            self::$rateLimitCache[$key] = [];
        }
        
        $attempts = count(self::$rateLimitCache[$key]);
        
        if ($attempts >= $maxAttempts) {
            // Progressive delay based on attempts
            $delay = min(pow(2, $attempts - $maxAttempts), 3600); // Max 1 hour
            
            self::logSecurityEvent('RATE_LIMIT_EXCEEDED', [
                'identifier' => $identifier,
                'attempts' => $attempts,
                'delay' => $delay
            ]);
            
            return [
                'allowed' => false,
                'message' => "Too many attempts. Try again in " . gmdate('H:i:s', $delay),
                'retry_after' => $delay
            ];
        }
        
        // Log this attempt
        self::$rateLimitCache[$key][] = $now;
        
        return ['allowed' => true, 'remaining' => $maxAttempts - $attempts - 1];
    }
    
    /**
     * Secure Session Management
     */
    public static function generateSecureToken($length = 64) {
        return bin2hex(random_bytes($length / 2));
    }
    
    public static function validateSessionToken($token) {
        // Check token format
        if (!preg_match('/^[a-f0-9]{64}$/', $token)) {
            return false;
        }
        
        // Check if token exists and is valid
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("
                SELECT us.*, u.email 
                FROM user_sessions us 
                JOIN users u ON us.user_id = u.id 
                WHERE us.session_token = ? AND us.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $session = $stmt->fetch();
            
            if (!$session) {
                return false;
            }
            
            // Update last activity
            $stmt = $db->prepare("UPDATE user_sessions SET last_activity = NOW() WHERE session_token = ?");
            $stmt->execute([$token]);
            
            return $session;
        } catch (Exception $e) {
            self::logSecurityEvent('SESSION_VALIDATION_ERROR', $e->getMessage());
            return false;
        }
    }
    
    /**
     * CSRF Protection
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        $token = self::generateSecureToken(32);
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_time'] = time();
        
        return $token;
    }
    
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_time'])) {
            return false;
        }
        
        // Token expires after 1 hour
        if (time() - $_SESSION['csrf_time'] > 3600) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_time']);
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Request Validation
     */
    public static function validateRequest() {
        // Check request method
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
        if (!in_array($_SERVER['REQUEST_METHOD'], $allowedMethods)) {
            throw new SecurityException('Invalid request method');
        }
        
        // Check content length
        $maxSize = 10 * 1024 * 1024; // 10MB
        if (isset($_SERVER['CONTENT_LENGTH']) && $_SERVER['CONTENT_LENGTH'] > $maxSize) {
            throw new SecurityException('Request too large');
        }
        
        // Check for suspicious headers
        $suspiciousHeaders = ['X-Forwarded-Host', 'X-Original-URL', 'X-Rewrite-URL'];
        foreach ($suspiciousHeaders as $header) {
            if (isset($_SERVER['HTTP_' . str_replace('-', '_', strtoupper($header))])) {
                self::logSecurityEvent('SUSPICIOUS_HEADER', $header);
            }
        }
        
        return true;
    }
    
    /**
     * IP Validation and Blocking
     */
    public static function validateIP($ip) {
        // Block private/reserved IPs in production
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return false;
        }
        
        // Check against known malicious IPs (implement your blacklist)
        $blacklistedIPs = ['127.0.0.1']; // Add your blacklist
        if (in_array($ip, $blacklistedIPs)) {
            self::logSecurityEvent('BLACKLISTED_IP', $ip);
            return false;
        }
        
        return true;
    }
    
    /**
     * Security Event Logging
     */
    public static function logSecurityEvent($event, $details = null) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'details' => $details
        ];
        
        // Log to file (ensure directory exists and is writable)
        $logFile = __DIR__ . '/../logs/security.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
        
        // Also log to database if available
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("
                INSERT INTO security_logs (event_type, ip_address, user_agent, details, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $event,
                $logEntry['ip'],
                $logEntry['user_agent'],
                json_encode($details)
            ]);
        } catch (Exception $e) {
            // Fail silently for logging
        }
    }
    
    /**
     * Get Real Client IP
     */
    public static function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_REAL_IP',            // Nginx
            'HTTP_CLIENT_IP',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return '127.0.0.1';
    }
    
    /**
     * Password Security Validation
     */
    public static function validatePasswordSecurity($password) {
        $errors = [];
        
        // Length check
        if (strlen($password) < 12) {
            $errors[] = 'Password must be at least 12 characters long';
        }
        
        // Complexity checks
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        // Check against common passwords
        $commonPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein'];
        if (in_array(strtolower($password), $commonPasswords)) {
            $errors[] = 'Password is too common';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => self::calculatePasswordStrength($password)
        ];
    }
    
    private static function calculatePasswordStrength($password) {
        $score = 0;
        $length = strlen($password);
        
        // Length scoring
        if ($length >= 8) $score += 1;
        if ($length >= 12) $score += 1;
        if ($length >= 16) $score += 1;
        
        // Character variety
        if (preg_match('/[a-z]/', $password)) $score += 1;
        if (preg_match('/[A-Z]/', $password)) $score += 1;
        if (preg_match('/[0-9]/', $password)) $score += 1;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 1;
        
        // Patterns
        if (preg_match('/(.)\1{2,}/', $password)) $score -= 1; // Repeated characters
        if (preg_match('/123|abc|qwe/i', $password)) $score -= 1; // Sequential
        
        $strength = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong', 'Very Strong'];
        return $strength[max(0, min(5, $score))];
    }
}

/**
 * Custom Security Exception
 */
class SecurityException extends Exception {
    public function __construct($message = "", $code = 403, Throwable $previous = null) {
        parent::__construct($message, $code, $previous);
        EnterpriseSecurity::logSecurityEvent('SECURITY_EXCEPTION', $message);
    }
}
?>
