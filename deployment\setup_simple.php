<!DOCTYPE html>
<html>
<head>
    <title>PriceDrop Alert - Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #f0fff0; padding: 10px; border-radius: 5px; }
        .error { color: red; background: #fff0f0; padding: 10px; border-radius: 5px; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>🚀 PriceDrop Alert - Simple Setup</h1>
    
    <?php
require_once __DIR__ . '/backend/config/database_simple.php';
require_once __DIR__ . '/backend/includes/security.php';
require_once __DIR__ . '/backend/includes/auth_simple.php';
require_once __DIR__ . '/backend/includes/product_manager.php';
    
    $tests = [];
    
    // Test 1: Database Connection
    echo "<div class='test-section'>";
    echo "<h3>📡 Database Connection Test</h3>";
    try {
        $db = Database::getInstance();
        echo "<div class='success'>✅ Database connection successful!</div>";
        $tests['database'] = true;
    } catch (Exception $e) {
        echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
        $tests['database'] = false;
    }
    echo "</div>";
    
    // Test 2: Create Tables
    echo "<div class='test-section'>";
    echo "<h3>📋 Database Tables</h3>";
    if ($tests['database']) {
        try {
            Database::createTables();
            echo "<div class='success'>✅ Database tables created successfully!</div>";
            $tests['tables'] = true;
        } catch (Exception $e) {
            echo "<div class='error'>❌ Failed to create tables: " . $e->getMessage() . "</div>";
            $tests['tables'] = false;
        }
    } else {
        echo "<div class='error'>❌ Cannot create tables - database connection failed</div>";
        $tests['tables'] = false;
    }
    echo "</div>";
    
    // Test 3: Enhanced User Registration
    echo "<div class='test-section'>";
    echo "<h3>👤 Enhanced User Registration Test</h3>";
    if ($tests['tables']) {
        try {
            $testEmail = 'test_' . time() . '@example.com';
            $result = SimpleAuth::register($testEmail, 'TestPass123!', 'Test User');

            if ($result['success']) {
                echo "<div class='success'>✅ Enhanced registration works! Created user ID: " . $result['user']['id'] . "</div>";
                echo "<div class='info'>📧 Email: " . $result['user']['email'] . "</div>";
                echo "<div class='info'>👤 Name: " . $result['user']['display_name'] . "</div>";
                echo "<div class='info'>🔐 Password hashing: Argon2ID</div>";
                $tests['registration'] = true;

                // Test login with session
                $loginResult = SimpleAuth::login($testEmail, 'TestPass123!');
                if ($loginResult['success'] && isset($loginResult['session_token'])) {
                    echo "<div class='success'>✅ Login with session token works!</div>";
                    echo "<div class='info'>🔑 Session token generated</div>";
                    $tests['login_session'] = true;
                } else {
                    echo "<div class='error'>❌ Login with session failed</div>";
                    $tests['login_session'] = false;
                }

                // Clean up test user
                $db = Database::getInstance();
                $stmt = $db->prepare("DELETE FROM users WHERE email = ?");
                $stmt->execute([$testEmail]);
                echo "<div class='info'>ℹ️ Test user cleaned up</div>";
            } else {
                echo "<div class='error'>❌ Enhanced registration failed: " . $result['message'] . "</div>";
                if (isset($result['details'])) {
                    foreach ($result['details'] as $detail) {
                        echo "<div class='error'>  • $detail</div>";
                    }
                }
                $tests['registration'] = false;
                $tests['login_session'] = false;
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Enhanced registration test failed: " . $e->getMessage() . "</div>";
            $tests['registration'] = false;
            $tests['login_session'] = false;
        }
    } else {
        echo "<div class='error'>❌ Cannot test registration - tables not created</div>";
        $tests['registration'] = false;
        $tests['login_session'] = false;
    }
    echo "</div>";

    // Test 4: Security Features
    echo "<div class='test-section'>";
    echo "<h3>🔒 Security Features Test</h3>";
    try {
        // Test rate limiting
        $rateLimitResult = Security::checkLoginRateLimit('127.0.0.1', '<EMAIL>');
        if ($rateLimitResult['allowed']) {
            echo "<div class='success'>✅ Rate limiting system active</div>";
        } else {
            echo "<div class='info'>ℹ️ Rate limiting triggered: " . $rateLimitResult['message'] . "</div>";
        }

        // Test password validation
        $weakPassword = Security::validatePasswordStrength('123');
        $strongPassword = Security::validatePasswordStrength('StrongPass123!');

        if (!$weakPassword['valid'] && $strongPassword['valid']) {
            echo "<div class='success'>✅ Password strength validation works</div>";
            echo "<div class='info'>  • Weak passwords rejected</div>";
            echo "<div class='info'>  • Strong passwords accepted</div>";
        } else {
            echo "<div class='error'>❌ Password validation not working properly</div>";
        }

        // Test input sanitization
        $dirtyInput = '<script>alert("xss")</script>';
        $cleanInput = Security::sanitizeInput($dirtyInput);
        if ($cleanInput !== $dirtyInput) {
            echo "<div class='success'>✅ Input sanitization active</div>";
        } else {
            echo "<div class='error'>❌ Input sanitization not working</div>";
        }

        $tests['security'] = true;
    } catch (Exception $e) {
        echo "<div class='error'>❌ Security test failed: " . $e->getMessage() . "</div>";
        $tests['security'] = false;
    }
    echo "</div>";
    
    // Test 5: Enhanced API Endpoints
    echo "<div class='test-section'>";
    echo "<h3>🌐 Enhanced API Endpoints</h3>";
    $baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    echo "<p><strong>API Base URL:</strong> $baseUrl/api_simple.php</p>";
    echo "<h4>Authentication:</h4>";
    echo "<ul>";
    echo "<li><a href='{$baseUrl}/api_simple.php?path=status' target='_blank'>Status Check</a></li>";
    echo "<li>Register: POST ?path=auth/register</li>";
    echo "<li>Login: POST ?path=auth/login</li>";
    echo "<li>Logout: POST ?path=auth/logout</li>";
    echo "</ul>";
    echo "<h4>User Management:</h4>";
    echo "<ul>";
    echo "<li>Profile: GET/PUT ?path=user/profile</li>";
    echo "<li>Preferences: PUT ?path=user/preferences</li>";
    echo "<li>Statistics: GET ?path=user/statistics</li>";
    echo "</ul>";
    echo "<h4>Product Management:</h4>";
    echo "<ul>";
    echo "<li>Products: GET/POST ?path=products</li>";
    echo "<li>Product Details: GET ?path=products/{id}</li>";
    echo "<li>Update Product: PUT ?path=products/{id}</li>";
    echo "<li>Delete Product: DELETE ?path=products/{id}</li>";
    echo "<li>Scrape Product: POST ?path=products/scrape</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 5: Current Database Status
    echo "<div class='test-section'>";
    echo "<h3>📊 Current Database Status</h3>";
    if ($tests['database']) {
        try {
            $db = Database::getInstance();
            
            // Count users
            $stmt = $db->query("SELECT COUNT(*) as count FROM users");
            $userCount = $stmt->fetch()['count'];
            echo "<p><strong>Total Users:</strong> $userCount</p>";
            
            // Count products
            $stmt = $db->query("SELECT COUNT(*) as count FROM products");
            $productCount = $stmt->fetch()['count'];
            echo "<p><strong>Total Products:</strong> $productCount</p>";
            
            // Show recent users
            if ($userCount > 0) {
                echo "<h4>Recent Users:</h4>";
                $stmt = $db->query("SELECT id, email, display_name, created_at FROM users ORDER BY created_at DESC LIMIT 5");
                $users = $stmt->fetchAll();
                
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>Email</th><th>Name</th><th>Created</th></tr>";
                foreach ($users as $user) {
                    echo "<tr>";
                    echo "<td>" . $user['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['display_name']) . "</td>";
                    echo "<td>" . $user['created_at'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error checking database status: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // Summary
    echo "<div class='test-section'>";
    echo "<h3>📋 Enhanced Setup Summary</h3>";
    $allPassed = array_reduce($tests, function($carry, $test) { return $carry && $test; }, true);

    if ($allPassed) {
        echo "<div class='success'>";
        echo "<h4>🎉 All Enhanced Tests Passed!</h4>";
        echo "<p>Your PriceDrop Alert backend is fully ready with all features!</p>";
        echo "<p><strong>✅ Features Available:</strong></p>";
        echo "<ul>";
        echo "<li>🔐 Secure Authentication (Argon2ID hashing)</li>";
        echo "<li>🛡️ Security Features (Rate limiting, Input sanitization)</li>";
        echo "<li>👤 User Profile Management</li>";
        echo "<li>📦 Product Management</li>";
        echo "<li>🔍 Product Scraping</li>";
        echo "<li>📊 User Statistics</li>";
        echo "<li>🔑 Session Management</li>";
        echo "</ul>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li>Update your Flutter app's API URL to: <code>{$baseUrl}/api_simple.php</code></li>";
        echo "<li>Test enhanced user registration from your Flutter app</li>";
        echo "<li>Test product management features</li>";
        echo "<li>Test user profile and statistics</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h4>❌ Some Tests Failed</h4>";
        echo "<p>Please fix the issues above before proceeding.</p>";
        echo "<p><strong>Failed Tests:</strong></p>";
        echo "<ul>";
        foreach ($tests as $testName => $result) {
            if (!$result) {
                echo "<li>❌ " . ucfirst(str_replace('_', ' ', $testName)) . "</li>";
            }
        }
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";
    ?>
    
    <div class="test-section">
        <h3>🔄 Actions</h3>
        <button onclick="location.reload()">Refresh Tests</button>
        <button onclick="window.open('<?php echo $baseUrl; ?>/api_simple.php?path=status', '_blank')">Test API</button>
    </div>
    
</body>
</html>
