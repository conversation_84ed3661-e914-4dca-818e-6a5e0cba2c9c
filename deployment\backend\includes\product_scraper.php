<?php
/**
 * Clean Product Scraper Interface
 * Uses the professional scraper engine
 */

require_once __DIR__ . '/product_scraper_professional.php';

class ProductScraper {
    private $professionalScraper;
    
    public function __construct() {
        $this->professionalScraper = new ProductScraperProfessional();
    }
    
    public function scrapeProductData($url) {
        return $this->professionalScraper->scrapeProductData($url);
    }
    
    // Legacy method for backward compatibility
    public function detectPlatform($url) {
        $domain = parse_url($url, PHP_URL_HOST);
        $domain = strtolower($domain);
        
        if (strpos($domain, 'amazon.') !== false) return 'Amazon';
        if (strpos($domain, 'flipkart.') !== false) return 'Flipkart';
        if (strpos($domain, 'myntra.') !== false) return 'Myntra';
        if (strpos($domain, 'ajio.') !== false) return 'Ajio';
        if (strpos($domain, 'nykaa.') !== false) return 'Nykaa';
        if (strpos($domain, 'meesho.') !== false) return 'Meesho';
        if (strpos($domain, 'snapdeal.') !== false) return 'Snapdeal';
        
        return 'Generic';
    }
}
