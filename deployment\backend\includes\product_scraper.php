<?php
/**
 * Product Scraper Class
 */

class ProductScraper {
    private $userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ];
    
    public function scrapeProductData($url) {
        $platform = $this->detectPlatform($url);

        // Try multiple times with different user agents
        $maxRetries = 3;
        $lastError = null;

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                switch ($platform) {
                    case 'Amazon':
                        return $this->scrapeAmazon($url, $attempt);
                    case 'Flipkart':
                        return $this->scrapeFlipkart($url, $attempt);
                    case 'Myntra':
                        return $this->scrapeMyntra($url, $attempt);
                    case 'Ajio':
                        return $this->scrapeAjio($url, $attempt);
                    case 'Nykaa':
                        return $this->scrapeNykaa($url, $attempt);
                    default:
                        return $this->scrapeGeneric($url, $attempt);
                }
            } catch (Exception $e) {
                $lastError = $e->getMessage();
                error_log("Scraping attempt $attempt failed for $url: " . $lastError);

                if ($attempt < $maxRetries) {
                    sleep(2); // Wait 2 seconds before retry
                }
            }
        }

        // If all attempts failed, try fallback method
        return $this->scrapeFallback($url);
    }
    

    
    private function scrapeAmazon($url, $attempt = 1) {
        $html = $this->fetchPage($url, $attempt);
        if (!$html) return false;

        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        // Multiple selectors for Amazon
        $titleSelectors = [
            '//*[@id="productTitle"]',
            '//*[contains(@class, "product-title")]',
            '//h1[contains(@class, "a-size-large")]'
        ];

        $priceSelectors = [
            '//*[contains(@class, "a-price-whole")]',
            '//*[contains(@class, "a-offscreen")]',
            '//*[@class="a-price a-text-price a-size-medium apexPriceToPay"]//span[@class="a-offscreen"]',
            '//*[@id="priceblock_dealprice"]',
            '//*[@id="priceblock_ourprice"]'
        ];

        $imageSelectors = [
            '//*[@id="landingImage"]',
            '//*[@id="imgBlkFront"]',
            '//img[contains(@class, "a-dynamic-image")]'
        ];

        $title = $this->extractTextFromSelectors($xpath, $titleSelectors);
        $price = $this->extractPriceFromSelectors($xpath, $priceSelectors);
        $image = $this->extractAttributeFromSelectors($xpath, $imageSelectors, 'src');

        return [
            'title' => $title ?: 'Product from Amazon',
            'price' => $price ?: 0,
            'image_url' => $image ?: '',
            'platform' => 'Amazon',
            'currency' => 'INR'
        ];
    }
    
    private function scrapeFlipkart($url, $attempt = 1) {
        // For Flipkart, try alternative methods due to heavy anti-bot protection
        if ($attempt === 1) {
            // Try mobile version first (less protected)
            $mobileUrl = str_replace('www.flipkart.com', 'm.flipkart.com', $url);
            $html = $this->fetchPageMobile($mobileUrl, $attempt);
        } else {
            $html = $this->fetchPage($url, $attempt);
        }

        if (!$html) {
            // Try API-like approach
            return $this->scrapeFlipkartAPI($url);
        }

        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        // Multiple selectors for Flipkart (they change frequently)
        $titleSelectors = [
            '//*[contains(@class, "B_NuCI")]',
            '//*[contains(@class, "_35KyD6")]',
            '//h1[contains(@class, "x2Jnpn")]',
            '//span[contains(@class, "B_NuCI")]'
        ];

        $priceSelectors = [
            '//*[contains(@class, "_30jeq3")]',
            '//*[contains(@class, "_1_WHN1")]',
            '//*[contains(@class, "_3I9_wc")]',
            '//*[contains(@class, "_25b18c")]'
        ];

        $imageSelectors = [
            '//*[contains(@class, "_396cs4")]',
            '//*[contains(@class, "_2r_T1I")]',
            '//img[contains(@class, "_396cs4")]'
        ];

        $title = $this->extractTextFromSelectors($xpath, $titleSelectors);
        $price = $this->extractPriceFromSelectors($xpath, $priceSelectors);
        $image = $this->extractAttributeFromSelectors($xpath, $imageSelectors, 'src');

        // If still no data, try JSON-LD extraction
        if (!$title || !$price) {
            $jsonData = $this->extractJsonLd($html);
            if ($jsonData) {
                $title = $title ?: ($jsonData['name'] ?? '');
                $price = $price ?: $this->parsePrice($jsonData['offers']['price'] ?? '');
                $image = $image ?: ($jsonData['image'] ?? '');
            }
        }

        return [
            'title' => $title ?: 'Product from Flipkart',
            'price' => $price ?: 0,
            'image_url' => $image ?: '',
            'platform' => 'Flipkart',
            'currency' => 'INR'
        ];
    }
    
    private function scrapeMyntra($url) {
        $html = $this->fetchPage($url);
        if (!$html) return false;
        
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);
        
        // Extract product data
        $title = $this->extractText($xpath, '//*[contains(@class, "pdp-title")]');
        $price = $this->extractPrice($xpath, '//*[contains(@class, "pdp-price")]');
        $image = $this->extractAttribute($xpath, '//*[contains(@class, "image-grid-image")]', 'src');
        
        return [
            'title' => $title,
            'price' => $price,
            'image_url' => $image,
            'platform' => 'Myntra',
            'currency' => 'INR'
        ];
    }
    
    private function scrapeAjio($url) {
        $html = $this->fetchPage($url);
        if (!$html) return false;
        
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);
        
        // Extract product data
        $title = $this->extractText($xpath, '//*[contains(@class, "prod-name")]');
        $price = $this->extractPrice($xpath, '//*[contains(@class, "prod-sp")]');
        $image = $this->extractAttribute($xpath, '//*[contains(@class, "img-cover")]', 'src');
        
        return [
            'title' => $title,
            'price' => $price,
            'image_url' => $image,
            'platform' => 'Ajio',
            'currency' => 'INR'
        ];
    }
    
    private function scrapeNykaa($url) {
        $html = $this->fetchPage($url);
        if (!$html) return false;
        
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);
        
        // Extract product data
        $title = $this->extractText($xpath, '//*[contains(@class, "product-title")]');
        $price = $this->extractPrice($xpath, '//*[contains(@class, "post-card__content-price-offer")]');
        $image = $this->extractAttribute($xpath, '//*[contains(@class, "product-image")]', 'src');
        
        return [
            'title' => $title,
            'price' => $price,
            'image_url' => $image,
            'platform' => 'Nykaa',
            'currency' => 'INR'
        ];
    }
    
    private function scrapeGeneric($url) {
        $html = $this->fetchPage($url);
        if (!$html) return false;
        
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);
        
        // Try common selectors
        $title = $this->extractText($xpath, '//title') ?: 
                 $this->extractText($xpath, '//h1') ?: 
                 'Unknown Product';
        
        // Try to find price in common patterns
        $price = $this->extractGenericPrice($html);
        
        // Try to find main image
        $image = $this->extractAttribute($xpath, '//meta[@property="og:image"]', 'content') ?:
                $this->extractAttribute($xpath, '//img[1]', 'src');
        
        return [
            'title' => $title,
            'price' => $price,
            'image_url' => $image,
            'platform' => 'Generic',
            'currency' => 'INR'
        ];
    }
    
    private function extractText($xpath, $query) {
        $nodes = $xpath->query($query);
        return $nodes->length > 0 ? trim($nodes->item(0)->textContent) : '';
    }
    
    private function extractAttribute($xpath, $query, $attribute) {
        $nodes = $xpath->query($query);
        return $nodes->length > 0 ? $nodes->item(0)->getAttribute($attribute) : '';
    }
    
    private function extractPrice($xpath, $query) {
        $priceText = $this->extractText($xpath, $query);
        return $this->parsePrice($priceText);
    }
    
    private function parsePrice($priceText) {
        // Remove currency symbols and extract numeric value
        $priceText = preg_replace('/[^\d.,]/', '', $priceText);
        $priceText = str_replace(',', '', $priceText);
        
        if (is_numeric($priceText)) {
            return floatval($priceText);
        }
        
        return 0.0;
    }
    
    private function extractGenericPrice($html) {
        // Try to find price patterns in HTML
        $patterns = [
            '/₹\s*([0-9,]+\.?[0-9]*)/i',
            '/INR\s*([0-9,]+\.?[0-9]*)/i',
            '/Rs\.?\s*([0-9,]+\.?[0-9]*)/i',
            '/price["\s:]*([0-9,]+\.?[0-9]*)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                return $this->parsePrice($matches[1]);
            }
        }
        
        return 0.0;
    }
    
    private function fetchPage($url, $attempt = 1) {
        $userAgent = $this->userAgents[($attempt - 1) % count($this->userAgents)];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, ''); // Enable compression
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.9,hi;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'Sec-Fetch-Dest: document',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-Site: none',
            'Cache-Control: max-age=0'
        ]);

        // Add random delay to appear more human-like
        if ($attempt > 1) {
            usleep(rand(1000000, 3000000)); // 1-3 seconds
        }

        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        if ($httpCode !== 200) {
            throw new Exception("HTTP error: $httpCode");
        }

        return $html;
    }

    private function extractTextFromSelectors($xpath, $selectors) {
        foreach ($selectors as $selector) {
            $text = $this->extractText($xpath, $selector);
            if (!empty($text)) {
                return $text;
            }
        }
        return '';
    }

    private function extractPriceFromSelectors($xpath, $selectors) {
        foreach ($selectors as $selector) {
            $price = $this->extractPrice($xpath, $selector);
            if ($price > 0) {
                return $price;
            }
        }
        return 0;
    }

    private function extractAttributeFromSelectors($xpath, $selectors, $attribute) {
        foreach ($selectors as $selector) {
            $attr = $this->extractAttribute($xpath, $selector, $attribute);
            if (!empty($attr)) {
                return $attr;
            }
        }
        return '';
    }

    private function extractJsonLd($html) {
        // Try to extract JSON-LD structured data
        if (preg_match('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $html, $matches)) {
            $jsonData = json_decode($matches[1], true);
            if ($jsonData && isset($jsonData['@type']) && $jsonData['@type'] === 'Product') {
                return $jsonData;
            }
        }
        return null;
    }

    private function scrapeFallback($url) {
        // Fallback method using basic URL parsing and generic extraction
        try {
            $html = $this->fetchPageSimple($url);
            if (!$html) return false;

            $dom = new DOMDocument();
            @$dom->loadHTML($html);
            $xpath = new DOMXPath($dom);

            // Extract title from meta tags or title tag
            $title = $this->extractAttribute($xpath, '//meta[@property="og:title"]', 'content') ?:
                    $this->extractAttribute($xpath, '//meta[@name="title"]', 'content') ?:
                    $this->extractText($xpath, '//title') ?:
                    'Product';

            // Extract price using regex patterns
            $price = $this->extractGenericPrice($html);

            // Extract image from meta tags
            $image = $this->extractAttribute($xpath, '//meta[@property="og:image"]', 'content') ?:
                    $this->extractAttribute($xpath, '//meta[@name="image"]', 'content');

            return [
                'title' => trim($title),
                'price' => $price,
                'image_url' => $image,
                'platform' => $this->detectPlatform($url),
                'currency' => 'INR'
            ];

        } catch (Exception $e) {
            error_log("Fallback scraping failed: " . $e->getMessage());
            return false;
        }
    }

    private function fetchPageSimple($url) {
        // Simple fetch without anti-bot headers
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (compatible; PriceBot/1.0)\r\n",
                'timeout' => 30
            ]
        ]);

        return @file_get_contents($url, false, $context);
    }

    private function fetchPageMobile($url, $attempt = 1) {
        $mobileUserAgents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'
        ];

        $userAgent = $mobileUserAgents[($attempt - 1) % count($mobileUserAgents)];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.9,hi;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'Sec-Fetch-Dest: document',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-Site: none',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ]);

        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error || $httpCode !== 200) {
            return false;
        }

        return $html;
    }

    private function scrapeFlipkartAPI($url) {
        // Extract product ID from URL
        preg_match('/\/p\/([a-zA-Z0-9]+)/', $url, $matches);
        if (!$matches) {
            preg_match('/pid=([a-zA-Z0-9]+)/', $url, $matches);
        }

        if (!$matches) {
            return $this->scrapeFlipkartFallback($url);
        }

        $productId = $matches[1];

        // Try to get basic info from URL structure
        $urlParts = parse_url($url);
        $pathParts = explode('/', trim($urlParts['path'], '/'));

        $title = '';
        foreach ($pathParts as $part) {
            if (strlen($part) > 10 && !preg_match('/^[a-zA-Z0-9]{10,}$/', $part)) {
                $title = str_replace('-', ' ', $part);
                $title = ucwords($title);
                break;
            }
        }

        return [
            'title' => $title ?: 'Product from Flipkart',
            'price' => 0, // Will be updated by cron job
            'image_url' => '',
            'platform' => 'Flipkart',
            'currency' => 'INR',
            'note' => 'Price will be updated automatically'
        ];
    }

    private function scrapeFlipkartFallback($url) {
        // Last resort: extract title from URL and return basic info
        $urlParts = parse_url($url);
        $path = $urlParts['path'] ?? '';

        // Extract product name from URL path
        $pathParts = explode('/', trim($path, '/'));
        $productName = '';

        foreach ($pathParts as $part) {
            if (strlen($part) > 5 && strpos($part, '-') !== false) {
                $productName = str_replace('-', ' ', $part);
                $productName = ucwords(strtolower($productName));
                break;
            }
        }

        return [
            'title' => $productName ?: 'Flipkart Product',
            'price' => 0,
            'image_url' => '',
            'platform' => 'Flipkart',
            'currency' => 'INR',
            'note' => 'Product added successfully. Price will be updated automatically.'
        ];
    }

    public function createBasicProduct($url, $platform) {
        // Extract product name from URL
        $urlParts = parse_url($url);
        $path = $urlParts['path'] ?? '';
        $pathParts = explode('/', trim($path, '/'));

        $productName = $platform . ' Product';
        foreach ($pathParts as $part) {
            if (strlen($part) > 5 && strpos($part, '-') !== false) {
                $productName = str_replace('-', ' ', $part);
                $productName = ucwords(strtolower($productName));
                break;
            }
        }

        return [
            'title' => $productName,
            'price' => 0,
            'image_url' => '',
            'platform' => $platform,
            'currency' => 'INR',
            'note' => 'Product added successfully. Price will be updated automatically by our monitoring system.'
        ];
    }

    private function detectPlatform($url) {
        if (strpos($url, 'amazon.') !== false) {
            return 'Amazon';
        } elseif (strpos($url, 'flipkart.com') !== false) {
            return 'Flipkart';
        } elseif (strpos($url, 'myntra.com') !== false) {
            return 'Myntra';
        } elseif (strpos($url, 'ajio.com') !== false) {
            return 'Ajio';
        } elseif (strpos($url, 'nykaa.com') !== false) {
            return 'Nykaa';
        } else {
            return 'Other';
        }
    }

    // Public method for external access
    public function getPlatform($url) {
        return $this->detectPlatform($url);
    }
}
?>
