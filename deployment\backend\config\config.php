<?php
/**
 * Simple Configuration for PriceDrop Alert
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'mobilego_pricedrop');
define('DB_USER', 'mobilego_priceuser');
define('DB_PASS', 'PriceAlert@2024');
define('DB_CHARSET', 'utf8mb4');

// API Configuration
define('API_BASE_URL', 'http://pricedropalert.mobunite.com/api');

// Error Reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('UTC');
define('UPLOAD_PATH', __DIR__ . '/../../uploads/');

// Cron Settings
define('PRICE_CHECK_INTERVAL', 3600); // 1 hour in seconds
define('MAX_PRODUCTS_PER_USER', 100);

// Supported Platforms
$SUPPORTED_PLATFORMS = [
    'Amazon' => [
        'name' => 'Amazon',
        'domains' => ['amazon.in', 'amazon.com'],
        'selectors' => [
            'title' => '#productTitle',
            'price' => '.a-price-whole',
            'image' => '#landingImage',
            'category' => '#wayfinding-breadcrumbs_feature_div a'
        ]
    ],
    'Flipkart' => [
        'name' => 'Flipkart',
        'domains' => ['flipkart.com'],
        'selectors' => [
            'title' => '.B_NuCI',
            'price' => '._30jeq3._16Jk6d',
            'image' => '._396cs4._2amPTt._3qGmMb',
            'category' => '._1HEvv0'
        ]
    ],
    'Myntra' => [
        'name' => 'Myntra',
        'domains' => ['myntra.com'],
        'selectors' => [
            'title' => '.pdp-title',
            'price' => '.pdp-price',
            'image' => '.image-grid-image',
            'category' => '.breadcrumbs-item'
        ]
    ],
    'Ajio' => [
        'name' => 'Ajio',
        'domains' => ['ajio.com'],
        'selectors' => [
            'title' => '.prod-name',
            'price' => '.prod-sp',
            'image' => '.img-cover',
            'category' => '.breadcrumb-item'
        ]
    ],
    'Nykaa' => [
        'name' => 'Nykaa',
        'domains' => ['nykaa.com'],
        'selectors' => [
            'title' => '.product-title',
            'price' => '.post-card__content-price-offer',
            'image' => '.product-image',
            'category' => '.breadcrumb'
        ]
    ]
];

// Error Reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/error.log');

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
ini_set('session.use_strict_mode', 1);
?>
