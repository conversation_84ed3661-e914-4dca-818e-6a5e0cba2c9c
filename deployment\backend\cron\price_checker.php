<?php
/**
 * Price Checker Cron Job
 * Checks product prices and sends notifications when targets are reached
 * 
 * Usage: php price_checker.php
 * Cron: 0 */1 * * * /usr/bin/php /path/to/price_checker.php
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/product_scraper.php';
require_once __DIR__ . '/../includes/notification_sender.php';

class PriceChecker {
    private $pdo;
    private $scraper;
    private $notificationSender;
    private $startTime;
    
    public function __construct() {
        $this->pdo = Database::getInstance();
        $this->scraper = new ProductScraper();
        $this->notificationSender = new NotificationSender();
        $this->startTime = microtime(true);
    }
    
    public function run() {
        $this->logCronStart();
        
        try {
            echo "Starting price check at " . date('Y-m-d H:i:s') . "\n";
            
            // Get all active products
            $products = $this->getActiveProducts();
            echo "Found " . count($products) . " active products to check\n";
            
            $checkedCount = 0;
            $notificationsSent = 0;
            
            foreach ($products as $product) {
                try {
                    $result = $this->checkProductPrice($product);
                    if ($result) {
                        $checkedCount++;
                        if ($result['notification_sent']) {
                            $notificationsSent++;
                        }
                    }
                    
                    // Add small delay to avoid overwhelming servers
                    usleep(500000); // 0.5 seconds
                    
                } catch (Exception $e) {
                    echo "Error checking product {$product['id']}: " . $e->getMessage() . "\n";
                    error_log("Price check error for product {$product['id']}: " . $e->getMessage());
                }
            }
            
            $executionTime = microtime(true) - $this->startTime;
            echo "Price check completed. Checked: $checkedCount, Notifications sent: $notificationsSent\n";
            echo "Execution time: " . round($executionTime, 2) . " seconds\n";
            
            $this->logCronComplete($checkedCount, $notificationsSent, $executionTime);
            
        } catch (Exception $e) {
            echo "Fatal error: " . $e->getMessage() . "\n";
            error_log("Price checker fatal error: " . $e->getMessage());
            $this->logCronFailed($e->getMessage());
        }
    }
    
    private function getActiveProducts() {
        $stmt = $this->pdo->prepare("
            SELECT p.*, u.email as user_email, u.display_name as user_name
            FROM products p
            JOIN users u ON p.user_id = u.id
            WHERE p.is_active = 1 
            AND (p.last_checked IS NULL OR p.last_checked < DATE_SUB(NOW(), INTERVAL ? SECOND))
            ORDER BY p.last_checked ASC
            LIMIT 100
        ");
        
        $stmt->execute([PRICE_CHECK_INTERVAL]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function checkProductPrice($product) {
        echo "Checking product: {$product['title']} (ID: {$product['id']})\n";
        
        // Scrape current price
        $scrapedData = $this->scraper->scrapeProductData($product['url']);
        
        if (!$scrapedData || !isset($scrapedData['price']) || $scrapedData['price'] <= 0) {
            echo "  Failed to get price data\n";
            $this->updateLastChecked($product['id']);
            return false;
        }
        
        $currentPrice = $scrapedData['price'];
        $previousPrice = $product['current_price'];
        
        echo "  Previous price: ₹{$previousPrice}, Current price: ₹{$currentPrice}\n";
        
        // Update product with new price
        $this->updateProductPrice($product['id'], $currentPrice, $scrapedData);
        
        // Add to price history
        $this->addPriceHistory($product['id'], $currentPrice);
        
        // Check if target is reached
        $notificationSent = false;
        if ($this->isTargetReached($product, $currentPrice)) {
            echo "  🎯 Target reached! Sending notification...\n";
            $this->sendTargetReachedNotification($product, $currentPrice);
            $notificationSent = true;
        } elseif ($currentPrice < $previousPrice) {
            echo "  📉 Price dropped by ₹" . ($previousPrice - $currentPrice) . "\n";
            $this->sendPriceDropNotification($product, $currentPrice, $previousPrice);
            $notificationSent = true;
        }
        
        return [
            'checked' => true,
            'notification_sent' => $notificationSent,
            'price_change' => $currentPrice - $previousPrice
        ];
    }
    
    private function isTargetReached($product, $currentPrice) {
        // Check target price
        if ($product['target_price'] > 0 && $currentPrice <= $product['target_price']) {
            return true;
        }
        
        // Check target percentage
        if ($product['target_percentage'] > 0 && $product['original_price'] > 0) {
            $discountPercentage = (($product['original_price'] - $currentPrice) / $product['original_price']) * 100;
            if ($discountPercentage >= $product['target_percentage']) {
                return true;
            }
        }
        
        return false;
    }
    
    private function updateProductPrice($productId, $price, $scrapedData) {
        $stmt = $this->pdo->prepare("
            UPDATE products 
            SET current_price = ?, 
                title = COALESCE(?, title),
                image_url = COALESCE(?, image_url),
                last_checked = NOW(),
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->execute([
            $price,
            $scrapedData['title'] ?? null,
            $scrapedData['image_url'] ?? null,
            $productId
        ]);
    }
    
    private function updateLastChecked($productId) {
        $stmt = $this->pdo->prepare("UPDATE products SET last_checked = NOW() WHERE id = ?");
        $stmt->execute([$productId]);
    }
    
    private function addPriceHistory($productId, $price) {
        $stmt = $this->pdo->prepare("INSERT INTO price_history (product_id, price) VALUES (?, ?)");
        $stmt->execute([$productId, $price]);
    }
    
    private function sendTargetReachedNotification($product, $currentPrice) {
        $this->notificationSender->sendTargetReachedNotification(
            $product['user_id'],
            $product,
            $currentPrice
        );
        
        // Log notification in database
        $this->logNotification($product['user_id'], $product['id'], 'target_reached', 
            "Target reached for {$product['title']}", 
            "Your target price of ₹{$product['target_price']} has been reached! Current price: ₹{$currentPrice}"
        );
    }
    
    private function sendPriceDropNotification($product, $currentPrice, $previousPrice) {
        $this->notificationSender->sendPriceDropNotification(
            $product['user_id'],
            $product,
            $currentPrice,
            $previousPrice
        );
        
        // Log notification in database
        $savings = $previousPrice - $currentPrice;
        $this->logNotification($product['user_id'], $product['id'], 'price_drop',
            "Price drop for {$product['title']}", 
            "Price dropped by ₹{$savings}! New price: ₹{$currentPrice}"
        );
    }
    
    private function logNotification($userId, $productId, $type, $title, $message) {
        $stmt = $this->pdo->prepare("
            INSERT INTO notifications (user_id, product_id, type, title, message) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $productId, $type, $title, $message]);
    }
    
    private function logCronStart() {
        $stmt = $this->pdo->prepare("
            INSERT INTO cron_logs (job_name, status, message) 
            VALUES ('price_checker', 'started', 'Price checking job started')
        ");
        $stmt->execute();
    }
    
    private function logCronComplete($checkedCount, $notificationsSent, $executionTime) {
        $stmt = $this->pdo->prepare("
            INSERT INTO cron_logs (job_name, status, message, execution_time) 
            VALUES ('price_checker', 'completed', ?, ?)
        ");
        $stmt->execute([
            "Checked {$checkedCount} products, sent {$notificationsSent} notifications",
            $executionTime
        ]);
    }
    
    private function logCronFailed($error) {
        $stmt = $this->pdo->prepare("
            INSERT INTO cron_logs (job_name, status, message, execution_time) 
            VALUES ('price_checker', 'failed', ?, ?)
        ");
        $stmt->execute([$error, microtime(true) - $this->startTime]);
    }
}

// Run the price checker
$checker = new PriceChecker();
$checker->run();
?>
