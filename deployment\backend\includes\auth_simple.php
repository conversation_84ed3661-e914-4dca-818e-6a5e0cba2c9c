<?php
/**
 * Simple Authentication System
 */

require_once __DIR__ . '/../config/database_simple.php';
require_once __DIR__ . '/enterprise_security.php';

class SimpleAuth {
    
    public static function register($email, $password, $displayName = null) {
        try {
            // Enterprise security validation
            EnterpriseSecurity::validateRequest();

            $db = Database::getInstance();
            $clientIP = EnterpriseSecurity::getClientIP();

            // Rate limiting check
            $rateLimitCheck = EnterpriseSecurity::checkRateLimit("register_$clientIP", 3, 3600);
            if (!$rateLimitCheck['allowed']) {
                return ['success' => false, 'message' => $rateLimitCheck['message']];
            }

            // Sanitize input
            $email = EnterpriseSecurity::sanitizeInput($email);
            $displayName = EnterpriseSecurity::sanitizeInput($displayName);

            // Validate input
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return ['success' => false, 'message' => 'Invalid email format'];
            }

            // Enterprise password validation
            $passwordValidation = EnterpriseSecurity::validatePasswordSecurity($password);
            if (!$passwordValidation['valid']) {
                return [
                    'success' => false,
                    'message' => 'Password requirements not met',
                    'details' => $passwordValidation['errors']
                ];
            }
            
            // Check if user exists
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'User already exists'];
            }
            
            // Enterprise-grade password hashing
            $hashedPassword = password_hash($password, PASSWORD_ARGON2ID, [
                'memory_cost' => 131072, // 128 MB
                'time_cost' => 6,        // 6 iterations
                'threads' => 4           // 4 threads
            ]);

            // Store password in history to prevent reuse
            $stmt = $db->prepare("SELECT password_hash FROM password_history WHERE user_id = (SELECT id FROM users WHERE email = ?) ORDER BY created_at DESC LIMIT 5");
            $stmt->execute([$email]);
            $oldPasswords = $stmt->fetchAll(PDO::FETCH_COLUMN);

            foreach ($oldPasswords as $oldHash) {
                if (password_verify($password, $oldHash)) {
                    return ['success' => false, 'message' => 'Cannot reuse recent passwords'];
                }
            }
            
            // Insert user
            $stmt = $db->prepare("
                INSERT INTO users (email, password_hash, display_name)
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$email, $hashedPassword, $displayName]);

            $userId = $db->lastInsertId();

            // Store password in history
            $stmt = $db->prepare("INSERT INTO password_history (user_id, password_hash) VALUES (?, ?)");
            $stmt->execute([$userId, $hashedPassword]);

            // Create default user preferences
            $stmt = $db->prepare("
                INSERT INTO user_preferences (user_id, email_notifications, push_notifications, price_drop_threshold)
                VALUES (?, 1, 1, 10.00)
            ");
            $stmt->execute([$userId]);

            // Log successful registration
            EnterpriseSecurity::logSecurityEvent('USER_REGISTERED', [
                'user_id' => $userId,
                'email' => $email,
                'ip' => $clientIP
            ]);

            return [
                'success' => true,
                'user' => [
                    'id' => $userId,
                    'email' => $email,
                    'display_name' => $displayName,
                    'photo_url' => null,
                    'email_verified' => false,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Registration failed'];
        }
    }
    
    public static function login($email, $password, $clientIP = null) {
        try {
            $db = Database::getInstance();

            // Enterprise security validation
            EnterpriseSecurity::validateRequest();

            // Sanitize input
            $email = EnterpriseSecurity::sanitizeInput($email);
            $clientIP = $clientIP ?: EnterpriseSecurity::getClientIP();

            // Multi-layer rate limiting
            $ipRateLimit = EnterpriseSecurity::checkRateLimit("login_ip_$clientIP", 10, 3600);
            $emailRateLimit = EnterpriseSecurity::checkRateLimit("login_email_$email", 5, 1800);

            if (!$ipRateLimit['allowed']) {
                EnterpriseSecurity::logSecurityEvent('LOGIN_RATE_LIMIT_IP', ['ip' => $clientIP]);
                return ['success' => false, 'message' => $ipRateLimit['message']];
            }

            if (!$emailRateLimit['allowed']) {
                EnterpriseSecurity::logSecurityEvent('LOGIN_RATE_LIMIT_EMAIL', ['email' => $email]);
                return ['success' => false, 'message' => $emailRateLimit['message']];
            }

            // Get user
            $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            $loginSuccess = false;
            if ($user && password_verify($password, $user['password_hash'])) {
                $loginSuccess = true;

                // Update last login
                $stmt = $db->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);

                // Get user preferences
                $stmt = $db->prepare("SELECT * FROM user_preferences WHERE user_id = ?");
                $stmt->execute([$user['id']]);
                $preferences = $stmt->fetch();

                // Create secure session token
                $sessionToken = EnterpriseSecurity::generateSecureToken();
                $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

                $stmt = $db->prepare("
                    INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, expires_at)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$user['id'], $sessionToken, $clientIP, $userAgent, $expiresAt]);

                // Log successful login
                EnterpriseSecurity::logSecurityEvent('LOGIN_SUCCESS', [
                    'user_id' => $user['id'],
                    'email' => $email,
                    'ip' => $clientIP
                ]);
            }

            // Log the attempt
            Security::logLoginAttempt($clientIP, $email, $loginSuccess);

            if (!$loginSuccess) {
                return ['success' => false, 'message' => 'Invalid email or password'];
            }

            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'display_name' => $user['display_name'],
                    'photo_url' => $user['photo_url'],
                    'email_verified' => (bool)$user['email_verified'],
                    'created_at' => $user['created_at'],
                    'preferences' => $preferences
                ],
                'session_token' => $sessionToken
            ];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Login failed'];
        }
    }

    /**
     * Verify session token
     */
    public static function verifySession($sessionToken) {
        try {
            $db = Database::getInstance();

            $stmt = $db->prepare("
                SELECT u.*, up.* FROM users u
                JOIN user_sessions us ON u.id = us.user_id
                LEFT JOIN user_preferences up ON u.id = up.user_id
                WHERE us.session_token = ? AND us.expires_at > NOW()
            ");
            $stmt->execute([$sessionToken]);
            $user = $stmt->fetch();

            if (!$user) {
                return ['success' => false, 'message' => 'Invalid or expired session'];
            }

            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'display_name' => $user['display_name'],
                    'photo_url' => $user['photo_url'],
                    'email_verified' => (bool)$user['email_verified'],
                    'preferences' => [
                        'email_notifications' => (bool)$user['email_notifications'],
                        'push_notifications' => (bool)$user['push_notifications'],
                        'price_drop_threshold' => $user['price_drop_threshold'],
                        'notification_frequency' => $user['notification_frequency']
                    ]
                ]
            ];

        } catch (Exception $e) {
            error_log("Session verification error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Session verification failed'];
        }
    }

    /**
     * Update user profile
     */
    public static function updateProfile($userId, $data) {
        try {
            $db = Database::getInstance();

            $updates = [];
            $params = [];

            if (isset($data['display_name'])) {
                $updates[] = "display_name = ?";
                $params[] = Security::sanitizeInput($data['display_name']);
            }

            if (isset($data['photo_url'])) {
                $updates[] = "photo_url = ?";
                $params[] = Security::sanitizeInput($data['photo_url']);
            }

            if (!empty($updates)) {
                $params[] = $userId;
                $sql = "UPDATE users SET " . implode(', ', $updates) . ", updated_at = NOW() WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
            }

            return ['success' => true, 'message' => 'Profile updated successfully'];

        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Profile update failed'];
        }
    }

    /**
     * Update user preferences
     */
    public static function updatePreferences($userId, $preferences) {
        try {
            $db = Database::getInstance();

            $stmt = $db->prepare("
                UPDATE user_preferences SET
                    email_notifications = ?,
                    push_notifications = ?,
                    price_drop_threshold = ?,
                    notification_frequency = ?,
                    updated_at = NOW()
                WHERE user_id = ?
            ");

            $stmt->execute([
                $preferences['email_notifications'] ?? true,
                $preferences['push_notifications'] ?? true,
                $preferences['price_drop_threshold'] ?? 10.00,
                $preferences['notification_frequency'] ?? 'instant',
                $userId
            ]);

            return ['success' => true, 'message' => 'Preferences updated successfully'];

        } catch (Exception $e) {
            error_log("Preferences update error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Preferences update failed'];
        }
    }

    /**
     * Logout user (invalidate session)
     */
    public static function logout($sessionToken) {
        try {
            $db = Database::getInstance();

            $stmt = $db->prepare("DELETE FROM user_sessions WHERE session_token = ?");
            $stmt->execute([$sessionToken]);

            return ['success' => true, 'message' => 'Logged out successfully'];

        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Logout failed'];
        }
    }

    /**
     * Update user profile
     */
    public static function updateProfile($userId, $data) {
        try {
            $db = Database::getInstance();

            $displayName = EnterpriseSecurity::sanitizeInput($data['display_name'] ?? '');
            $photoUrl = EnterpriseSecurity::sanitizeInput($data['photo_url'] ?? '');

            $stmt = $db->prepare("UPDATE users SET display_name = ?, photo_url = ? WHERE id = ?");
            $stmt->execute([$displayName, $photoUrl, $userId]);

            return ['success' => true, 'message' => 'Profile updated successfully'];

        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Profile update failed'];
        }
    }

    /**
     * Get notification settings
     */
    public static function getNotificationSettings($userId) {
        try {
            $db = Database::getInstance();

            $stmt = $db->prepare("
                SELECT email_notifications, push_notifications, fcm_token
                FROM user_preferences
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $settings = $stmt->fetch();

            return [
                'success' => true,
                'settings' => $settings ?: [
                    'email_notifications' => true,
                    'push_notifications' => true,
                    'fcm_token' => null
                ]
            ];

        } catch (Exception $e) {
            error_log("Get notification settings error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to get notification settings'];
        }
    }

    /**
     * Update notification settings
     */
    public static function updateNotificationSettings($userId, $data) {
        try {
            $db = Database::getInstance();

            $emailNotifications = (bool)($data['email_notifications'] ?? true);
            $pushNotifications = (bool)($data['push_notifications'] ?? true);

            $stmt = $db->prepare("
                UPDATE user_preferences
                SET email_notifications = ?, push_notifications = ?
                WHERE user_id = ?
            ");
            $stmt->execute([$emailNotifications, $pushNotifications, $userId]);

            return ['success' => true, 'message' => 'Notification settings updated'];

        } catch (Exception $e) {
            error_log("Update notification settings error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to update notification settings'];
        }
    }

    /**
     * Update FCM token for push notifications
     */
    public static function updateFCMToken($userId, $token) {
        try {
            $db = Database::getInstance();

            $token = EnterpriseSecurity::sanitizeInput($token);

            $stmt = $db->prepare("
                UPDATE user_preferences
                SET fcm_token = ?
                WHERE user_id = ?
            ");
            $stmt->execute([$token, $userId]);

            return ['success' => true, 'message' => 'FCM token updated'];

        } catch (Exception $e) {
            error_log("Update FCM token error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to update FCM token'];
        }
    }
}
?>
