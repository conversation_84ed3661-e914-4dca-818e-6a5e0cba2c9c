<?php
/**
 * Professional Product Scraper Engine
 * Modern, robust scraping with anti-bot protection bypass
 */

class ProductScraperProfessional {
    private $userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    
    private $shortUrlDomains = [
        'amzn.to', 'amzn.in', 'a.co',
        'fkrt.it', 'dl.flipkart.com',
        'myntra.com/s/', 'myntra.com/w/',
        'ajio.com/s/', 'nykaa.com/s/',
        'bit.ly', 'tinyurl.com', 'goo.gl', 't.co'
    ];
    
    public function scrapeProductData($url) {
        try {
            // Expand shortened URLs
            $expandedUrl = $this->expandShortUrl($url);
            if ($expandedUrl !== $url) {
                error_log("✅ Expanded URL: $url -> $expandedUrl");
                $url = $expandedUrl;
            }
            
            $platform = $this->detectPlatform($url);
            error_log("🔍 Detected platform: $platform for URL: $url");

            // Try multiple strategies
            $strategies = [
                'direct' => [$this, 'scrapeDirect'],
                'mobile' => [$this, 'scrapeMobile'],
                'api' => [$this, 'scrapeAPI']
            ];

            foreach ($strategies as $strategyName => $strategy) {
                try {
                    error_log("🚀 Trying $strategyName strategy for $platform");
                    $result = call_user_func($strategy, $url, $platform);
                    
                    if ($result && $this->validateResult($result)) {
                        error_log("✅ Successfully scraped using $strategyName strategy");
                        return $result;
                    }
                } catch (Exception $e) {
                    error_log("❌ $strategyName strategy failed: " . $e->getMessage());
                }
                
                // Random delay between strategies
                sleep(rand(1, 3));
            }

            // Final fallback
            error_log("⚠️ All strategies failed, using intelligent fallback");
            return $this->intelligentFallback($url, $platform);
            
        } catch (Exception $e) {
            error_log("💥 Critical scraping error: " . $e->getMessage());
            return $this->emergencyFallback($url);
        }
    }
    
    private function expandShortUrl($url) {
        $parsedUrl = parse_url($url);
        $domain = $parsedUrl['host'] ?? '';
        
        // Check if it's a short URL
        $isShortUrl = false;
        foreach ($this->shortUrlDomains as $shortDomain) {
            if (strpos($domain, $shortDomain) !== false) {
                $isShortUrl = true;
                break;
            }
        }
        
        if (!$isShortUrl) {
            return $url;
        }
        
        // Follow redirects to get final URL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 15,
            CURLOPT_USERAGENT => $this->getRandomUserAgent(),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_NOBODY => true,
            CURLOPT_HEADER => true
        ]);
        
        curl_exec($ch);
        $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return ($httpCode >= 200 && $httpCode < 400 && $finalUrl) ? $finalUrl : $url;
    }
    
    private function detectPlatform($url) {
        $domain = parse_url($url, PHP_URL_HOST);
        $domain = strtolower($domain);
        
        if (strpos($domain, 'amazon.') !== false) return 'Amazon';
        if (strpos($domain, 'flipkart.') !== false) return 'Flipkart';
        if (strpos($domain, 'myntra.') !== false) return 'Myntra';
        if (strpos($domain, 'ajio.') !== false) return 'Ajio';
        if (strpos($domain, 'nykaa.') !== false) return 'Nykaa';
        if (strpos($domain, 'meesho.') !== false) return 'Meesho';
        if (strpos($domain, 'snapdeal.') !== false) return 'Snapdeal';
        
        return 'Generic';
    }
    
    private function scrapeDirect($url, $platform) {
        $html = $this->fetchPageAdvanced($url);
        if (!$html) return false;
        
        switch ($platform) {
            case 'Amazon':
                return $this->parseAmazon($html, $url);
            case 'Flipkart':
                return $this->parseFlipkart($html, $url);
            case 'Myntra':
                return $this->parseMyntra($html, $url);
            case 'Ajio':
                return $this->parseAjio($html, $url);
            case 'Nykaa':
                return $this->parseNykaa($html, $url);
            default:
                return $this->parseGeneric($html, $url);
        }
    }
    
    private function scrapeMobile($url, $platform) {
        // Convert to mobile version
        $mobileUrl = $this->convertToMobileUrl($url, $platform);
        $html = $this->fetchPageMobile($mobileUrl);
        
        if (!$html) return false;
        
        return $this->parseMobileVersion($html, $mobileUrl, $platform);
    }
    
    private function scrapeAPI($url, $platform) {
        // Try to extract product info from URL structure and meta tags
        $html = $this->fetchPageMinimal($url);
        if (!$html) return false;
        
        return $this->parseMetaData($html, $url, $platform);
    }
    
    private function fetchPageAdvanced($url, $attempt = 1) {
        $ch = curl_init();
        
        // Advanced headers to bypass anti-bot protection
        $headers = [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.5',
            'Accept-Encoding: gzip, deflate, br',
            'DNT: 1',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'Sec-Fetch-Dest: document',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-Site: none',
            'Cache-Control: max-age=0'
        ];
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 5,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => $this->getRandomUserAgent(),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_ENCODING => 'gzip, deflate',
            CURLOPT_COOKIEJAR => '/tmp/cookies_' . md5($url) . '.txt',
            CURLOPT_COOKIEFILE => '/tmp/cookies_' . md5($url) . '.txt'
        ]);
        
        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("CURL Error: $error");
            return false;
        }
        
        if ($httpCode !== 200) {
            error_log("HTTP Error: $httpCode for URL: $url");
            return false;
        }
        
        return $html;
    }
    
    private function getRandomUserAgent() {
        return $this->userAgents[array_rand($this->userAgents)];
    }
    
    private function validateResult($result) {
        return $result &&
               isset($result['title']) &&
               !empty($result['title']) &&
               $result['title'] !== 'Unknown Product';
    }

    private function parseAmazon($html, $url) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        // Multiple selectors for title
        $titleSelectors = [
            '//span[@id="productTitle"]',
            '//h1[@id="title"]//span[@id="productTitle"]',
            '//h1[contains(@class, "a-size-large")]',
            '//span[contains(@class, "product-title")]'
        ];

        $title = $this->extractBySelectors($xpath, $titleSelectors);

        // Multiple selectors for price
        $priceSelectors = [
            '//span[contains(@class, "a-price-whole")]',
            '//span[@class="a-price a-text-price a-size-medium a-color-base"]//span[@class="a-offscreen"]',
            '//span[@id="priceblock_dealprice"]',
            '//span[@id="priceblock_ourprice"]',
            '//span[contains(@class, "a-price-range")]'
        ];

        $priceText = $this->extractBySelectors($xpath, $priceSelectors);
        $price = $this->extractPrice($priceText);

        // Image
        $imageSelectors = [
            '//img[@id="landingImage"]/@src',
            '//img[contains(@class, "a-dynamic-image")]/@src',
            '//div[@id="imgTagWrapperId"]//img/@src'
        ];

        $imageUrl = $this->extractBySelectors($xpath, $imageSelectors);

        return [
            'title' => $title ?: $this->extractTitleFromUrl($url),
            'price' => $price,
            'image_url' => $imageUrl ?: '',
            'platform' => 'Amazon',
            'currency' => 'INR',
            'availability' => 'In Stock'
        ];
    }

    private function parseFlipkart($html, $url) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        // Flipkart title selectors
        $titleSelectors = [
            '//span[@class="B_NuCI"]',
            '//h1[contains(@class, "_35KyD6")]',
            '//span[contains(@class, "G6XhBx")]',
            '//h1//span[contains(@class, "yhB1nd")]'
        ];

        $title = $this->extractBySelectors($xpath, $titleSelectors);

        // Flipkart price selectors
        $priceSelectors = [
            '//div[contains(@class, "_30jeq3 _16Jk6d")]',
            '//div[contains(@class, "_1_WHN1")]',
            '//div[contains(@class, "_3I9_wc _2p6lqe")]',
            '//span[contains(@class, "_2-_7WA")]'
        ];

        $priceText = $this->extractBySelectors($xpath, $priceSelectors);
        $price = $this->extractPrice($priceText);

        // Image
        $imageSelectors = [
            '//img[contains(@class, "_396cs4")]/@src',
            '//img[contains(@class, "_2r_T1I")]/@src',
            '//div[contains(@class, "_3li7GG")]//img/@src'
        ];

        $imageUrl = $this->extractBySelectors($xpath, $imageSelectors);

        return [
            'title' => $title ?: $this->extractTitleFromUrl($url),
            'price' => $price,
            'image_url' => $imageUrl ?: '',
            'platform' => 'Flipkart',
            'currency' => 'INR',
            'availability' => 'In Stock'
        ];
    }

    private function extractBySelectors($xpath, $selectors) {
        foreach ($selectors as $selector) {
            $nodes = $xpath->query($selector);
            if ($nodes && $nodes->length > 0) {
                $value = trim($nodes->item(0)->textContent ?? $nodes->item(0)->nodeValue ?? '');
                if (!empty($value)) {
                    return $value;
                }
            }
        }
        return '';
    }

    private function extractPrice($priceText) {
        if (empty($priceText)) return 0;

        // Remove currency symbols and extract numbers
        $priceText = preg_replace('/[^\d.,]/', '', $priceText);
        $priceText = str_replace(',', '', $priceText);

        if (preg_match('/(\d+\.?\d*)/', $priceText, $matches)) {
            return floatval($matches[1]);
        }

        return 0;
    }

    private function extractTitleFromUrl($url) {
        $path = parse_url($url, PHP_URL_PATH);
        $segments = explode('/', $path);

        foreach ($segments as $segment) {
            if (strlen($segment) > 10 && strpos($segment, '-') !== false) {
                return ucwords(str_replace('-', ' ', $segment));
            }
        }

        return 'Product from ' . parse_url($url, PHP_URL_HOST);
    }

    private function parseMyntra($html, $url) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        $titleSelectors = [
            '//h1[contains(@class, "pdp-title")]',
            '//h1[contains(@class, "pdp-name")]',
            '//span[contains(@class, "pdp-product-name")]'
        ];

        $title = $this->extractBySelectors($xpath, $titleSelectors);

        $priceSelectors = [
            '//span[contains(@class, "pdp-price")]',
            '//span[contains(@class, "pdp-mrp")]',
            '//div[contains(@class, "price-info")]//span'
        ];

        $priceText = $this->extractBySelectors($xpath, $priceSelectors);
        $price = $this->extractPrice($priceText);

        return [
            'title' => $title ?: $this->extractTitleFromUrl($url),
            'price' => $price,
            'image_url' => '',
            'platform' => 'Myntra',
            'currency' => 'INR',
            'availability' => 'In Stock'
        ];
    }

    private function parseAjio($html, $url) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        $titleSelectors = [
            '//h1[contains(@class, "prod-title")]',
            '//div[contains(@class, "prod-name")]//h1'
        ];

        $title = $this->extractBySelectors($xpath, $titleSelectors);

        $priceSelectors = [
            '//span[contains(@class, "prod-sp")]',
            '//div[contains(@class, "prod-price")]//span'
        ];

        $priceText = $this->extractBySelectors($xpath, $priceSelectors);
        $price = $this->extractPrice($priceText);

        return [
            'title' => $title ?: $this->extractTitleFromUrl($url),
            'price' => $price,
            'image_url' => '',
            'platform' => 'Ajio',
            'currency' => 'INR',
            'availability' => 'In Stock'
        ];
    }

    private function parseNykaa($html, $url) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        $titleSelectors = [
            '//h1[contains(@class, "product-title")]',
            '//div[contains(@class, "product-name")]//h1'
        ];

        $title = $this->extractBySelectors($xpath, $titleSelectors);

        $priceSelectors = [
            '//span[contains(@class, "post-card__content-price-offer")]',
            '//div[contains(@class, "product-price")]//span'
        ];

        $priceText = $this->extractBySelectors($xpath, $priceSelectors);
        $price = $this->extractPrice($priceText);

        return [
            'title' => $title ?: $this->extractTitleFromUrl($url),
            'price' => $price,
            'image_url' => '',
            'platform' => 'Nykaa',
            'currency' => 'INR',
            'availability' => 'In Stock'
        ];
    }

    private function parseGeneric($html, $url) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        // Generic selectors that work on most sites
        $titleSelectors = [
            '//title',
            '//h1',
            '//meta[@property="og:title"]/@content',
            '//meta[@name="title"]/@content'
        ];

        $title = $this->extractBySelectors($xpath, $titleSelectors);

        // Try to find price in common patterns
        $priceSelectors = [
            '//*[contains(@class, "price")]',
            '//*[contains(@class, "cost")]',
            '//*[contains(@class, "amount")]',
            '//meta[@property="product:price:amount"]/@content'
        ];

        $priceText = $this->extractBySelectors($xpath, $priceSelectors);
        $price = $this->extractPrice($priceText);

        return [
            'title' => $title ?: $this->extractTitleFromUrl($url),
            'price' => $price,
            'image_url' => '',
            'platform' => parse_url($url, PHP_URL_HOST),
            'currency' => 'INR',
            'availability' => 'Available'
        ];
    }

    private function convertToMobileUrl($url, $platform) {
        switch ($platform) {
            case 'Amazon':
                return str_replace('www.amazon.', 'm.amazon.', $url);
            case 'Flipkart':
                return str_replace('www.flipkart.', 'm.flipkart.', $url);
            case 'Myntra':
                return str_replace('www.myntra.', 'm.myntra.', $url);
            default:
                return $url;
        }
    }

    private function fetchPageMobile($url) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 20,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return ($httpCode === 200) ? $html : false;
    }

    private function fetchPageMinimal($url) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_USERAGENT => $this->getRandomUserAgent(),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_NOBODY => false,
            CURLOPT_RANGE => '0-8192' // Only first 8KB
        ]);

        $html = curl_exec($ch);
        curl_close($ch);

        return $html;
    }

    private function parseMobileVersion($html, $url, $platform) {
        // Mobile versions often have simpler HTML
        return $this->parseGeneric($html, $url);
    }

    private function parseMetaData($html, $url, $platform) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        // Extract from meta tags
        $title = $this->extractBySelectors($xpath, [
            '//meta[@property="og:title"]/@content',
            '//meta[@name="twitter:title"]/@content',
            '//title'
        ]);

        $price = $this->extractPrice($this->extractBySelectors($xpath, [
            '//meta[@property="product:price:amount"]/@content',
            '//meta[@property="og:price:amount"]/@content'
        ]));

        return [
            'title' => $title ?: $this->extractTitleFromUrl($url),
            'price' => $price,
            'image_url' => '',
            'platform' => $platform,
            'currency' => 'INR',
            'availability' => 'Available'
        ];
    }

    private function intelligentFallback($url, $platform) {
        // Extract product info from URL structure
        $title = $this->extractTitleFromUrl($url);

        return [
            'title' => $title,
            'price' => 0,
            'image_url' => '',
            'platform' => $platform,
            'currency' => 'INR',
            'availability' => 'Available',
            'note' => 'Product details will be updated automatically'
        ];
    }

    private function emergencyFallback($url) {
        $domain = parse_url($url, PHP_URL_HOST);

        return [
            'title' => 'Product from ' . $domain,
            'price' => 0,
            'image_url' => '',
            'platform' => $domain,
            'currency' => 'INR',
            'availability' => 'Available',
            'note' => 'Product details will be updated automatically'
        ];
    }
}
