<?php
/**
 * Production Deployment Script
 * Optimized for LiteSpeed Shared Hosting
 */

require_once __DIR__ . '/backend/config/database_simple.php';
require_once __DIR__ . '/backend/includes/enterprise_security.php';

class ProductionDeployer {
    
    private $db;
    private $errors = [];
    private $warnings = [];
    private $success = [];
    
    public function __construct() {
        try {
            $this->db = Database::getInstance();
            $this->success[] = "✅ Database connection established";
        } catch (Exception $e) {
            $this->errors[] = "❌ Database connection failed: " . $e->getMessage();
        }
    }
    
    public function deploy() {
        echo "<h1>🚀 PriceDrop Alert - Production Deployment</h1>";
        echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 8px;'>";
        
        $this->checkEnvironment();
        $this->setupDatabase();
        $this->createDirectories();
        $this->setPermissions();
        $this->optimizeForLiteSpeed();
        $this->runSecurityTests();
        $this->cleanupUnnecessaryFiles();
        
        $this->displayResults();
    }
    
    private function checkEnvironment() {
        echo "<h2>🔍 Environment Check</h2>";
        
        // PHP Version
        if (version_compare(PHP_VERSION, '8.0.0', '>=')) {
            $this->success[] = "✅ PHP Version: " . PHP_VERSION;
        } else {
            $this->errors[] = "❌ PHP Version too old: " . PHP_VERSION . " (requires 8.0+)";
        }
        
        // Required Extensions
        $required = ['pdo', 'pdo_mysql', 'json', 'openssl', 'curl', 'mbstring'];
        foreach ($required as $ext) {
            if (extension_loaded($ext)) {
                $this->success[] = "✅ Extension: $ext";
            } else {
                $this->errors[] = "❌ Missing extension: $ext";
            }
        }
        
        // Memory Limit
        $memory = ini_get('memory_limit');
        if (intval($memory) >= 128) {
            $this->success[] = "✅ Memory Limit: $memory";
        } else {
            $this->warnings[] = "⚠️ Low memory limit: $memory (recommend 128M+)";
        }
        
        // LiteSpeed Detection
        if (isset($_SERVER['SERVER_SOFTWARE']) && strpos($_SERVER['SERVER_SOFTWARE'], 'LiteSpeed') !== false) {
            $this->success[] = "✅ LiteSpeed Web Server detected";
        } else {
            $this->warnings[] = "⚠️ LiteSpeed not detected, some optimizations may not apply";
        }
    }
    
    private function setupDatabase() {
        echo "<h2>🗄️ Database Setup</h2>";
        
        try {
            // Test database connection
            $this->db->query("SELECT 1");
            $this->success[] = "✅ Database connection working";
            
            // Check if tables exist
            $tables = ['users', 'products', 'user_sessions', 'security_logs'];
            foreach ($tables as $table) {
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $this->success[] = "✅ Table exists: $table";
                } else {
                    $this->warnings[] = "⚠️ Table missing: $table (will be created)";
                }
            }
            
            // Initialize database
            Database::initialize();
            $this->success[] = "✅ Database schema updated";
            
        } catch (Exception $e) {
            $this->errors[] = "❌ Database setup failed: " . $e->getMessage();
        }
    }
    
    private function createDirectories() {
        echo "<h2>📁 Directory Setup</h2>";
        
        $dirs = [
            'backend/logs',
            'backend/temp',
            'backend/cache',
            'uploads',
            'error'
        ];
        
        foreach ($dirs as $dir) {
            $fullPath = __DIR__ . '/' . $dir;
            if (!is_dir($fullPath)) {
                if (mkdir($fullPath, 0755, true)) {
                    $this->success[] = "✅ Created directory: $dir";
                } else {
                    $this->errors[] = "❌ Failed to create directory: $dir";
                }
            } else {
                $this->success[] = "✅ Directory exists: $dir";
            }
        }
    }
    
    private function setPermissions() {
        echo "<h2>🔒 Permission Setup</h2>";
        
        $permissions = [
            'backend/logs' => 0755,
            'backend/temp' => 0755,
            'backend/cache' => 0755,
            '.htaccess' => 0644,
            'api_simple.php' => 0644
        ];
        
        foreach ($permissions as $path => $perm) {
            $fullPath = __DIR__ . '/' . $path;
            if (file_exists($fullPath)) {
                if (chmod($fullPath, $perm)) {
                    $this->success[] = "✅ Set permissions: $path (" . decoct($perm) . ")";
                } else {
                    $this->warnings[] = "⚠️ Could not set permissions: $path";
                }
            }
        }
    }
    
    private function optimizeForLiteSpeed() {
        echo "<h2>⚡ LiteSpeed Optimization</h2>";
        
        // Create optimized .htaccess if it doesn't exist
        $htaccessPath = __DIR__ . '/.htaccess';
        if (!file_exists($htaccessPath)) {
            $this->warnings[] = "⚠️ .htaccess file missing - security rules not active";
        } else {
            $this->success[] = "✅ .htaccess file exists";
        }
        
        // Check for LiteSpeed Cache
        if (function_exists('litespeed_purge_all')) {
            $this->success[] = "✅ LiteSpeed Cache available";
        } else {
            $this->warnings[] = "⚠️ LiteSpeed Cache not available";
        }
        
        // Optimize PHP settings
        $optimizations = [
            'opcache.enable' => '1',
            'opcache.memory_consumption' => '128',
            'opcache.max_accelerated_files' => '4000'
        ];
        
        foreach ($optimizations as $setting => $value) {
            if (ini_get($setting) == $value) {
                $this->success[] = "✅ PHP setting optimized: $setting";
            } else {
                $this->warnings[] = "⚠️ PHP setting not optimized: $setting (current: " . ini_get($setting) . ")";
            }
        }
    }
    
    private function runSecurityTests() {
        echo "<h2>🛡️ Security Tests</h2>";
        
        try {
            // Test enterprise security
            EnterpriseSecurity::validateRequest();
            $this->success[] = "✅ Enterprise security framework loaded";
            
            // Test rate limiting
            $result = EnterpriseSecurity::checkRateLimit('test_deployment', 5, 60);
            if ($result['allowed']) {
                $this->success[] = "✅ Rate limiting functional";
            }
            
            // Test password validation
            $passTest = EnterpriseSecurity::validatePasswordSecurity('TestPassword123!');
            if ($passTest['valid']) {
                $this->success[] = "✅ Password validation working";
            }
            
            // Test session token generation
            $token = EnterpriseSecurity::generateSecureToken();
            if (strlen($token) === 64) {
                $this->success[] = "✅ Secure token generation working";
            }
            
        } catch (Exception $e) {
            $this->errors[] = "❌ Security test failed: " . $e->getMessage();
        }
    }
    
    private function cleanupUnnecessaryFiles() {
        echo "<h2>🧹 Cleanup</h2>";
        
        $unnecessaryFiles = [
            'README.md',
            'composer.json',
            'composer.lock',
            '.git',
            '.gitignore',
            'node_modules',
            'package.json',
            'yarn.lock'
        ];
        
        foreach ($unnecessaryFiles as $file) {
            $fullPath = __DIR__ . '/' . $file;
            if (file_exists($fullPath)) {
                if (is_dir($fullPath)) {
                    $this->warnings[] = "⚠️ Directory should be removed: $file";
                } else {
                    $this->warnings[] = "⚠️ File should be removed: $file";
                }
            } else {
                $this->success[] = "✅ Clean: $file not present";
            }
        }
    }
    
    private function displayResults() {
        echo "<h2>📊 Deployment Results</h2>";
        
        echo "<h3 style='color: green;'>✅ Success (" . count($this->success) . ")</h3>";
        foreach ($this->success as $msg) {
            echo "<div>$msg</div>";
        }
        
        if (!empty($this->warnings)) {
            echo "<h3 style='color: orange;'>⚠️ Warnings (" . count($this->warnings) . ")</h3>";
            foreach ($this->warnings as $msg) {
                echo "<div>$msg</div>";
            }
        }
        
        if (!empty($this->errors)) {
            echo "<h3 style='color: red;'>❌ Errors (" . count($this->errors) . ")</h3>";
            foreach ($this->errors as $msg) {
                echo "<div>$msg</div>";
            }
        }
        
        echo "<h2>🎯 Deployment Status</h2>";
        if (empty($this->errors)) {
            echo "<div style='color: green; font-size: 18px; font-weight: bold;'>🎉 DEPLOYMENT SUCCESSFUL!</div>";
            echo "<div>Your PriceDrop Alert backend is ready for production!</div>";
        } else {
            echo "<div style='color: red; font-size: 18px; font-weight: bold;'>❌ DEPLOYMENT FAILED</div>";
            echo "<div>Please fix the errors above before proceeding.</div>";
        }
        
        echo "</div>";
    }
}

// Run deployment
$deployer = new ProductionDeployer();
$deployer->deploy();
?>
