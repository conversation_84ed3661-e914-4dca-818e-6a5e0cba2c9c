name: price_drop_alert
description: A comprehensive price tracking app for smart shoppers
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  
  # UI & Theming
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_animate: ^4.3.0
  lottie: ^2.7.0
  
  # Navigation
  go_router: ^12.1.3
  
  # Network & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # FCM for push notifications only
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  
  # Sharing & URL handling
  share_plus: ^7.2.2
  url_launcher: ^6.2.2
  receive_sharing_intent: ^1.4.5
  
  # QR & Barcode
  mobile_scanner: ^3.5.6
  
  # Charts & Analytics
  fl_chart: ^0.65.0
  
  # Image handling
  cached_network_image: ^3.3.0
  
  # Permissions
  permission_handler: ^11.1.0
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.2.1
  html: ^0.15.4
  flutter_local_notifications: ^17.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  
  # Code generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.6
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
  
  # fonts:
  #   - family: PriceDropIcons
  #     fonts:
  #       - asset: assets/fonts/PriceDropIcons.ttf
