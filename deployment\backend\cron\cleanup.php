<?php
/**
 * Database Cleanup Cron Job
 * Cleans up old data and maintains database performance
 * 
 * Usage: php cleanup.php
 * Cron: 0 2 * * * /usr/bin/php /path/to/cleanup.php (daily at 2 AM)
 */

// Prevent web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

class DatabaseCleanup {
    private $pdo;
    private $startTime;
    
    public function __construct() {
        $this->pdo = Database::getInstance();
        $this->startTime = microtime(true);
    }
    
    public function run() {
        $this->logCleanupStart();
        
        try {
            echo "Starting database cleanup at " . date('Y-m-d H:i:s') . "\n";
            
            $totalCleaned = 0;
            
            // Clean old price history (keep last 90 days)
            $totalCleaned += $this->cleanOldPriceHistory();
            
            // Clean old notifications (keep last 30 days)
            $totalCleaned += $this->cleanOldNotifications();
            
            // Clean old cron logs (keep last 7 days)
            $totalCleaned += $this->cleanOldCronLogs();
            
            // Clean inactive FCM tokens (older than 30 days)
            $totalCleaned += $this->cleanInactiveFCMTokens();
            
            // Clean orphaned records
            $totalCleaned += $this->cleanOrphanedRecords();
            
            // Optimize tables
            $this->optimizeTables();
            
            $executionTime = microtime(true) - $this->startTime;
            echo "Database cleanup completed. Total records cleaned: $totalCleaned\n";
            echo "Execution time: " . round($executionTime, 2) . " seconds\n";
            
            $this->logCleanupComplete($totalCleaned, $executionTime);
            
        } catch (Exception $e) {
            echo "Fatal error: " . $e->getMessage() . "\n";
            error_log("Database cleanup fatal error: " . $e->getMessage());
            $this->logCleanupFailed($e->getMessage());
        }
    }
    
    private function cleanOldPriceHistory() {
        echo "Cleaning old price history (older than 90 days)...\n";
        
        $stmt = $this->pdo->prepare("
            DELETE FROM price_history 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
        ");
        $stmt->execute();
        
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned old price history records\n";
        
        return $cleaned;
    }
    
    private function cleanOldNotifications() {
        echo "Cleaning old notifications (older than 30 days)...\n";
        
        $stmt = $this->pdo->prepare("
            DELETE FROM notifications 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        $stmt->execute();
        
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned old notification records\n";
        
        return $cleaned;
    }
    
    private function cleanOldCronLogs() {
        echo "Cleaning old cron logs (older than 7 days)...\n";
        
        $stmt = $this->pdo->prepare("
            DELETE FROM cron_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");
        $stmt->execute();
        
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned old cron log records\n";
        
        return $cleaned;
    }
    
    private function cleanInactiveFCMTokens() {
        echo "Cleaning inactive FCM tokens (older than 30 days)...\n";
        
        $stmt = $this->pdo->prepare("
            DELETE FROM fcm_tokens 
            WHERE is_active = 0 
            AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        $stmt->execute();
        
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned inactive FCM token records\n";
        
        return $cleaned;
    }
    
    private function cleanOrphanedRecords() {
        echo "Cleaning orphaned records...\n";
        
        $totalCleaned = 0;
        
        // Clean price history for deleted products
        $stmt = $this->pdo->prepare("
            DELETE ph FROM price_history ph
            LEFT JOIN products p ON ph.product_id = p.id
            WHERE p.id IS NULL
        ");
        $stmt->execute();
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned orphaned price history records\n";
        $totalCleaned += $cleaned;
        
        // Clean notifications for deleted products
        $stmt = $this->pdo->prepare("
            DELETE n FROM notifications n
            LEFT JOIN products p ON n.product_id = p.id
            WHERE n.product_id IS NOT NULL AND p.id IS NULL
        ");
        $stmt->execute();
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned orphaned notification records\n";
        $totalCleaned += $cleaned;
        
        // Clean FCM tokens for deleted users
        $stmt = $this->pdo->prepare("
            DELETE f FROM fcm_tokens f
            LEFT JOIN users u ON f.user_id = u.id
            WHERE u.id IS NULL
        ");
        $stmt->execute();
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned orphaned FCM token records\n";
        $totalCleaned += $cleaned;
        
        // Clean user preferences for deleted users
        $stmt = $this->pdo->prepare("
            DELETE up FROM user_preferences up
            LEFT JOIN users u ON up.user_id = u.id
            WHERE u.id IS NULL
        ");
        $stmt->execute();
        $cleaned = $stmt->rowCount();
        echo "  Cleaned $cleaned orphaned user preference records\n";
        $totalCleaned += $cleaned;
        
        return $totalCleaned;
    }
    
    private function optimizeTables() {
        echo "Optimizing database tables...\n";
        
        $tables = [
            'users',
            'user_preferences', 
            'products',
            'price_history',
            'notifications',
            'fcm_tokens',
            'cron_logs'
        ];
        
        foreach ($tables as $table) {
            try {
                $this->pdo->exec("OPTIMIZE TABLE $table");
                echo "  Optimized table: $table\n";
            } catch (Exception $e) {
                echo "  Failed to optimize table $table: " . $e->getMessage() . "\n";
            }
        }
    }
    
    private function logCleanupStart() {
        $stmt = $this->pdo->prepare("
            INSERT INTO cron_logs (job_name, status, message) 
            VALUES ('database_cleanup', 'started', 'Database cleanup job started')
        ");
        $stmt->execute();
    }
    
    private function logCleanupComplete($totalCleaned, $executionTime) {
        $stmt = $this->pdo->prepare("
            INSERT INTO cron_logs (job_name, status, message, execution_time) 
            VALUES ('database_cleanup', 'completed', ?, ?)
        ");
        $stmt->execute([
            "Cleaned {$totalCleaned} records, optimized tables",
            $executionTime
        ]);
    }
    
    private function logCleanupFailed($error) {
        $stmt = $this->pdo->prepare("
            INSERT INTO cron_logs (job_name, status, message, execution_time) 
            VALUES ('database_cleanup', 'failed', ?, ?)
        ");
        $stmt->execute([$error, microtime(true) - $this->startTime]);
    }
}

// Run the cleanup
$cleanup = new DatabaseCleanup();
$cleanup->run();
?>
