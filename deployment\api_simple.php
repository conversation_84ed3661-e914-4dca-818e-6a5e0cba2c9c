<?php
/**
 * Simple API for PriceDrop Alert
 */

// Enterprise security headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-CSRF-Token');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');

// Enterprise security validation
try {
    EnterpriseSecurity::validateRequest();
} catch (SecurityException $e) {
    sendResponse(403, ['error' => 'Security violation detected']);
}

// Handle OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/backend/config/database_simple.php';
require_once __DIR__ . '/backend/includes/enterprise_security.php';
require_once __DIR__ . '/backend/includes/auth_simple.php';
require_once __DIR__ . '/backend/includes/product_manager.php';
require_once __DIR__ . '/backend/includes/product_scraper.php';

// Helper function to send JSON response
function sendResponse($code, $data) {
    http_response_code($code);
    echo json_encode($data);
    exit();
}

// Helper function to authenticate user
function authenticateUser() {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';

    if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        sendResponse(401, ['error' => 'Authentication required']);
    }

    $sessionToken = $matches[1];
    $authResult = SimpleAuth::verifySession($sessionToken);

    if (!$authResult['success']) {
        sendResponse(401, ['error' => 'Invalid or expired session']);
    }

    return $authResult['user'];
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true) ?? [];
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

try {
    // Ensure database tables exist
    Database::createTables();
    
    // Route requests
    switch ($path) {
        case 'auth/register':
            if ($method !== 'POST') {
                sendResponse(405, ['error' => 'Method not allowed']);
            }
            
            if (empty($input['email']) || empty($input['password'])) {
                sendResponse(400, ['error' => 'Email and password are required']);
            }
            
            $result = SimpleAuth::register(
                $input['email'],
                $input['password'],
                $input['display_name'] ?? null
            );
            
            if ($result['success']) {
                sendResponse(201, $result);
            } else {
                sendResponse(400, ['error' => $result['message']]);
            }
            break;
            
        case 'auth/login':
            if ($method !== 'POST') {
                sendResponse(405, ['error' => 'Method not allowed']);
            }
            
            if (empty($input['email']) || empty($input['password'])) {
                sendResponse(400, ['error' => 'Email and password are required']);
            }
            
            $clientIP = Security::getClientIP();
            $result = SimpleAuth::login($input['email'], $input['password'], $clientIP);
            
            if ($result['success']) {
                sendResponse(200, $result);
            } else {
                sendResponse(401, ['error' => $result['message']]);
            }
            break;
            
        case 'products':
            $user = authenticateUser();

            if ($method === 'GET') {
                $limit = intval($_GET['limit'] ?? 50);
                $offset = intval($_GET['offset'] ?? 0);
                $result = ProductManager::getUserProducts($user['id'], $limit, $offset);
                sendResponse(200, $result);

            } elseif ($method === 'POST') {
                if (empty($input['url'])) {
                    sendResponse(400, ['error' => 'URL is required']);
                }

                $result = ProductManager::addProduct($user['id'], $input);

                if ($result['success']) {
                    sendResponse(201, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        case 'products/scrape':
            if ($method !== 'POST') {
                sendResponse(405, ['error' => 'Method not allowed']);
            }

            if (empty($input['url'])) {
                sendResponse(400, ['error' => 'URL is required']);
            }

            $scraper = new ProductScraper();
            $result = $scraper->scrapeProductData($input['url']);

            if ($result) {
                sendResponse(200, ['success' => true, 'product' => $result]);
            } else {
                sendResponse(400, ['error' => 'Unable to scrape product data']);
            }
            break;

        // Handle product-specific endpoints (products/{id})
        default:
            if (preg_match('/^products\/(\d+)$/', $path, $matches)) {
                $user = authenticateUser();
                $productId = $matches[1];

                if ($method === 'GET') {
                    $result = ProductManager::getProductDetails($productId, $user['id']);
                    sendResponse(200, $result);

                } elseif ($method === 'PUT') {
                    $result = ProductManager::updateProduct($productId, $user['id'], $input);

                    if ($result['success']) {
                        sendResponse(200, $result);
                    } else {
                        sendResponse(400, ['error' => $result['message']]);
                    }

                } elseif ($method === 'DELETE') {
                    $result = ProductManager::deleteProduct($productId, $user['id']);

                    if ($result['success']) {
                        sendResponse(200, $result);
                    } else {
                        sendResponse(400, ['error' => $result['message']]);
                    }
                }
            }
            break;

        case 'user/profile':
            $user = authenticateUser();

            if ($method === 'GET') {
                sendResponse(200, ['success' => true, 'user' => $user]);

            } elseif ($method === 'PUT') {
                $result = SimpleAuth::updateProfile($user['id'], $input);

                if ($result['success']) {
                    sendResponse(200, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        case 'user/preferences':
            $user = authenticateUser();

            if ($method === 'PUT') {
                $result = SimpleAuth::updatePreferences($user['id'], $input);

                if ($result['success']) {
                    sendResponse(200, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        case 'user/statistics':
            $user = authenticateUser();
            $result = ProductManager::getUserStatistics($user['id']);
            sendResponse(200, $result);
            break;

        case 'auth/logout':
            if ($method !== 'POST') {
                sendResponse(405, ['error' => 'Method not allowed']);
            }

            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';

            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                $sessionToken = $matches[1];
                SimpleAuth::logout($sessionToken);
            }

            sendResponse(200, ['success' => true, 'message' => 'Logged out successfully']);
            break;

        case 'status':
            sendResponse(200, [
                'status' => 'OK',
                'message' => 'PriceDrop Alert API is running',
                'version' => '2.0.0',
                'features' => [
                    'secure_auth' => true,
                    'product_management' => true,
                    'price_tracking' => true,
                    'user_profiles' => true,
                    'statistics' => true
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;

        // User profile management
        case 'user/profile':
            $user = authenticateUser();

            if ($method === 'GET') {
                sendResponse(200, ['success' => true, 'user' => $user]);

            } elseif ($method === 'PUT') {
                $result = SimpleAuth::updateProfile($user['id'], $input);
                if ($result['success']) {
                    sendResponse(200, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        // User preferences
        case 'user/preferences':
            $user = authenticateUser();

            if ($method === 'GET') {
                $result = SimpleAuth::getUserPreferences($user['id']);
                sendResponse(200, $result);

            } elseif ($method === 'PUT') {
                $result = SimpleAuth::updatePreferences($user['id'], $input);
                if ($result['success']) {
                    sendResponse(200, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        // User statistics
        case 'user/statistics':
            $user = authenticateUser();

            if ($method === 'GET') {
                $result = ProductManager::getUserStatistics($user['id']);
                sendResponse(200, $result);
            }
            break;

        // Price alerts management
        case 'alerts':
            $user = authenticateUser();

            if ($method === 'GET') {
                $result = ProductManager::getUserAlerts($user['id']);
                sendResponse(200, $result);

            } elseif ($method === 'POST') {
                $result = ProductManager::createAlert($user['id'], $input);
                if ($result['success']) {
                    sendResponse(201, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        // Notification settings
        case 'notifications/settings':
            $user = authenticateUser();

            if ($method === 'GET') {
                $result = SimpleAuth::getNotificationSettings($user['id']);
                sendResponse(200, $result);

            } elseif ($method === 'PUT') {
                $result = SimpleAuth::updateNotificationSettings($user['id'], $input);
                if ($result['success']) {
                    sendResponse(200, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        // FCM token management
        case 'notifications/token':
            $user = authenticateUser();

            if ($method === 'POST') {
                $result = SimpleAuth::updateFCMToken($user['id'], $input['token'] ?? '');
                if ($result['success']) {
                    sendResponse(200, $result);
                } else {
                    sendResponse(400, ['error' => $result['message']]);
                }
            }
            break;

        // System status
        case 'status':
            sendResponse(200, [
                'success' => true,
                'status' => 'operational',
                'version' => '2.0.0',
                'timestamp' => date('Y-m-d H:i:s'),
                'security' => 'enterprise-grade'
            ]);
            break;

        default:
            sendResponse(404, ['error' => 'Endpoint not found']);
    }

} catch (SecurityException $e) {
    EnterpriseSecurity::logSecurityEvent('API_SECURITY_VIOLATION', $e->getMessage());
    sendResponse(403, ['error' => 'Security violation']);
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    sendResponse(500, ['error' => 'Internal server error']);
}
?>
