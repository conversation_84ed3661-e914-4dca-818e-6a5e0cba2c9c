
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/auth/presentation/providers/auth_provider.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/products/presentation/pages/add_product_page.dart';
import '../../features/products/presentation/pages/product_detail_page.dart';
import '../../features/products/presentation/pages/product_comparison_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/scanner/presentation/pages/scanner_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';
import '../../features/statistics/presentation/pages/statistics_page.dart';
import '../../features/onboarding/presentation/pages/onboarding_page.dart';
import '../models/product.dart';
import '../widgets/main_navigation.dart';

final appRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);
  
  return GoRouter(
    initialLocation: '/onboarding',
    redirect: (context, state) {
      final isLoggedIn = authState.when(
        data: (user) => user != null,
        loading: () => false,
        error: (_, __) => false,
      );

      final isOnboarding = state.uri.toString() == '/onboarding';
      final isAuth = state.uri.toString().startsWith('/auth');

      if (!isLoggedIn && !isAuth && !isOnboarding) {
        return '/auth/login';
      }

      if (isLoggedIn && (isAuth || isOnboarding)) {
        return '/home';
      }

      return null;
    },
    routes: [
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),
      
      GoRoute(
        path: '/auth/login',
        builder: (context, state) => const LoginPage(),
      ),
      
      GoRoute(
        path: '/auth/register',
        builder: (context, state) => const RegisterPage(),
      ),

      // Simple Auth Routes (for testing)

      
      ShellRoute(
        builder: (context, state, child) => MainNavigation(child: child),
        routes: [
          GoRoute(
            path: '/home',
            builder: (context, state) => const HomePage(),
          ),
          
          GoRoute(
            path: '/add-product',
            builder: (context, state) => const AddProductPage(),
          ),

          GoRoute(
            path: '/scanner',
            builder: (context, state) => const ScannerPage(),
          ),

          GoRoute(
            path: '/statistics',
            builder: (context, state) => const StatisticsPage(),
          ),

          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfilePage(),
          ),

          GoRoute(
            path: '/settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
      
      GoRoute(
        path: '/product/:id',
        builder: (context, state) => ProductDetailPage(
          productId: state.pathParameters['id']!,
        ),
      ),

      GoRoute(
        path: '/product/:id/compare',
        builder: (context, state) {
          // You'll need to fetch the product by ID here
          // For now, we'll pass a placeholder
          return ProductComparisonPage(
            product: state.extra as Product,
          );
        },
      ),
    ],
  );
});