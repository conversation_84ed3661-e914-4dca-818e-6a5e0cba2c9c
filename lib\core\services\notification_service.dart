import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    try {
      // Initialize local notifications
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Create notification channels
      await _createNotificationChannels();

      debugPrint('✅ Notification service initialized successfully');
    } catch (e) {
      debugPrint('⚠️ Notification service initialization failed: $e');
    }
  }

  static Future<void> _createNotificationChannels() async {
    const priceDropChannel = AndroidNotificationChannel(
      'price_drop_channel',
      'Price Drop Alerts',
      description: 'Notifications for price drops',
      importance: Importance.high,
    );

    const targetReachedChannel = AndroidNotificationChannel(
      'target_reached_channel',
      'Target Reached',
      description: 'Notifications when target price is reached',
      importance: Importance.high,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(priceDropChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(targetReachedChannel);
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    // TODO: Navigate to appropriate page based on payload
  }



  static Future<void> showPriceDropNotification({
    required String productId,
    required String productTitle,
    required double oldPrice,
    required double newPrice,
    required String imageUrl,
  }) async {
    final discountPercentage = ((oldPrice - newPrice) / oldPrice * 100);

    const androidDetails = AndroidNotificationDetails(
      'price_drop_channel',
      'Price Drop Alerts',
      channelDescription: 'Notifications for price drops',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      productId.hashCode,
      '🎉 Price Drop Alert!',
      '$productTitle is now ₹${newPrice.toStringAsFixed(0)} (${discountPercentage.toStringAsFixed(1)}% off)',
      details,
      payload: productId,
    );
  }

  static Future<void> showTargetReachedNotification({
    required String productId,
    required String productTitle,
    required double targetPrice,
    required double currentPrice,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'target_reached_channel',
      'Target Reached',
      channelDescription: 'Notifications when target price is reached',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      productId.hashCode,
      '🎯 Target Reached!',
      '$productTitle has reached your target price of ₹${targetPrice.toStringAsFixed(0)}',
      details,
      payload: productId,
    );
  }

}
