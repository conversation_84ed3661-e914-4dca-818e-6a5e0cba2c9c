import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'product.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class Product extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final String url;
  
  @HiveField(3)
  final String imageUrl;
  
  @HiveField(4)
  final double currentPrice;
  
  @HiveField(5)
  final double originalPrice;
  
  @HiveField(6)
  final double targetPrice;
  
  @HiveField(7)
  final double targetPercentage;
  
  @HiveField(8)
  final String platform;
  
  @HiveField(9)
  final String category;
  
  @HiveField(10)
  final DateTime createdAt;
  
  @HiveField(11)
  final DateTime updatedAt;
  
  @HiveField(12)
  final bool isActive;
  
  @HiveField(13)
  final List<PriceHistory> priceHistory;
  
  @HiveField(14)
  final String? affiliateUrl;

  Product({
    required this.id,
    required this.title,
    required this.url,
    required this.imageUrl,
    required this.currentPrice,
    required this.originalPrice,
    required this.targetPrice,
    required this.targetPercentage,
    required this.platform,
    required this.category,
    required this.createdAt,
    required this.updatedAt,
    required this.isActive,
    required this.priceHistory,
    this.affiliateUrl,
  });

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToJson(this);

  double get discountPercentage {
    if (originalPrice == 0) return 0;
    return ((originalPrice - currentPrice) / originalPrice) * 100;
  }

  bool get hasReachedTarget {
    return currentPrice <= targetPrice || discountPercentage >= targetPercentage;
  }

  Product copyWith({
    String? title,
    String? url,
    String? imageUrl,
    double? currentPrice,
    double? originalPrice,
    double? targetPrice,
    double? targetPercentage,
    String? platform,
    String? category,
    DateTime? updatedAt,
    bool? isActive,
    List<PriceHistory>? priceHistory,
    String? affiliateUrl,
  }) {
    return Product(
      id: id,
      title: title ?? this.title,
      url: url ?? this.url,
      imageUrl: imageUrl ?? this.imageUrl,
      currentPrice: currentPrice ?? this.currentPrice,
      originalPrice: originalPrice ?? this.originalPrice,
      targetPrice: targetPrice ?? this.targetPrice,
      targetPercentage: targetPercentage ?? this.targetPercentage,
      platform: platform ?? this.platform,
      category: category ?? this.category,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      priceHistory: priceHistory ?? this.priceHistory,
      affiliateUrl: affiliateUrl ?? this.affiliateUrl,
    );
  }
}

@HiveType(typeId: 1)
@JsonSerializable()
class PriceHistory extends HiveObject {
  @HiveField(0)
  final double price;
  
  @HiveField(1)
  final DateTime timestamp;

  PriceHistory({
    required this.price,
    required this.timestamp,
  });

  factory PriceHistory.fromJson(Map<String, dynamic> json) => _$PriceHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$PriceHistoryToJson(this);
}