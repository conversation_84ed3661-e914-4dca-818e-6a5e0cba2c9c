import 'package:flutter/foundation.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'dart:async';

class SharingIntentService {
  static SharingIntentService? _instance;
  static SharingIntentService get instance => _instance ??= SharingIntentService._();
  
  SharingIntentService._();
  
  StreamSubscription? _intentDataStreamSubscription;
  final StreamController<String> _sharedUrlController = StreamController<String>.broadcast();
  
  Stream<String> get sharedUrlStream => _sharedUrlController.stream;
  
  void initialize() {
    debugPrint('🔗 Initializing Sharing Intent Service');

    try {
      // Listen for shared content when app is already running
      _intentDataStreamSubscription = ReceiveSharingIntent.instance.getMediaStream().listen(
        (List<SharedMediaFile> value) {
          _handleSharedMedia(value);
        },
        onError: (err) {
          debugPrint('❌ Sharing intent error: $err');
        },
      );

      // Get any shared content when app was launched
      ReceiveSharingIntent.instance.getInitialMedia().then((List<SharedMediaFile> value) {
        _handleSharedMedia(value);
      });

      debugPrint('✅ Sharing Intent Service initialized');
    } catch (e) {
      debugPrint('⚠️ Sharing Intent Service initialization failed: $e');
    }
  }
  
  void _handleSharedMedia(List<SharedMediaFile> files) {
    for (final file in files) {
      if (file.type == SharedMediaType.text) {
        _handleSharedText(file.path);
      }
    }
  }
  
  void _handleSharedText(String text) {
    debugPrint('📤 Received shared text: $text');
    
    if (_isValidProductUrl(text)) {
      debugPrint('✅ Valid product URL detected: $text');
      _sharedUrlController.add(text);
    } else {
      debugPrint('⚠️ Invalid product URL: $text');
    }
  }
  
  bool _isValidProductUrl(String url) {
    if (url.isEmpty) return false;
    
    try {
      final uri = Uri.parse(url);
      final host = uri.host.toLowerCase();
      
      // Check for supported e-commerce platforms
      final supportedDomains = [
        'amazon.in',
        'www.amazon.in',
        'amazon.com',
        'www.amazon.com',
        'flipkart.com',
        'www.flipkart.com',
        'm.flipkart.com',
        'myntra.com',
        'www.myntra.com',
        'ajio.com',
        'www.ajio.com',
        'nykaa.com',
        'www.nykaa.com',
        'meesho.com',
        'www.meesho.com',
        'snapdeal.com',
        'www.snapdeal.com',
        // Shortened URLs
        'amzn.to',
        'amzn.in',
        'a.co',
        'fkrt.it',
        'dl.flipkart.com',
        'bit.ly',
        'tinyurl.com',
        'goo.gl',
        't.co'
      ];
      
      return supportedDomains.any((domain) => host.contains(domain));
    } catch (e) {
      debugPrint('❌ Error parsing URL: $e');
      return false;
    }
  }
  
  String getPlatformFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final host = uri.host.toLowerCase();
      
      if (host.contains('amazon')) return 'Amazon';
      if (host.contains('flipkart')) return 'Flipkart';
      if (host.contains('myntra')) return 'Myntra';
      if (host.contains('ajio')) return 'Ajio';
      if (host.contains('nykaa')) return 'Nykaa';
      if (host.contains('meesho')) return 'Meesho';
      if (host.contains('snapdeal')) return 'Snapdeal';
      
      // For shortened URLs, we'll detect after expansion
      return 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }
  
  void dispose() {
    _intentDataStreamSubscription?.cancel();
    _sharedUrlController.close();
  }
  
  // Method to manually trigger sharing (for testing)
  void simulateSharedUrl(String url) {
    _handleSharedText(url);
  }
  
  // Clear any pending shared URLs
  void clearSharedUrls() {
    // This will be handled by the stream listeners
  }
}

// Extension to make URL validation easier
extension UrlValidation on String {
  bool get isValidProductUrl {
    return SharingIntentService.instance._isValidProductUrl(this);
  }
  
  String get platformName {
    return SharingIntentService.instance.getPlatformFromUrl(this);
  }
}
