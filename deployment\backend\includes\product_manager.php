<?php
/**
 * Product Management System
 */

require_once __DIR__ . '/../config/database_simple.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/product_scraper.php';

class ProductManager {
    
    /**
     * Add a new product
     */
    public static function addProduct($userId, $productData) {
        try {
            $db = Database::getInstance();
            
            // Sanitize input
            $url = Security::sanitizeInput($productData['url']);
            $targetPrice = floatval($productData['target_price'] ?? 0);
            $targetPercentage = floatval($productData['target_percentage'] ?? 0);
            
            // Validate URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                return ['success' => false, 'message' => 'Invalid URL'];
            }
            
            // Check if product already exists for this user
            $stmt = $db->prepare("SELECT id FROM products WHERE user_id = ? AND url = ?");
            $stmt->execute([$userId, $url]);
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'Product already exists in your list'];
            }
            
            // Scrape product data
            $scraper = new ProductScraper();
            $scrapedData = $scraper->scrapeProductData($url);
            
            if (!$scrapedData) {
                return ['success' => false, 'message' => 'Unable to fetch product data from URL'];
            }
            
            // Insert product
            $stmt = $db->prepare("
                INSERT INTO products (
                    user_id, url, title, platform, original_price, current_price,
                    target_price, target_percentage, image_url, last_checked_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $url,
                $scrapedData['title'],
                $scrapedData['platform'],
                $scrapedData['price'],
                $scrapedData['price'],
                $targetPrice,
                $targetPercentage,
                $scrapedData['image_url']
            ]);
            
            $productId = $db->lastInsertId();
            
            // Add initial price history
            $stmt = $db->prepare("INSERT INTO price_history (product_id, price) VALUES (?, ?)");
            $stmt->execute([$productId, $scrapedData['price']]);
            
            return [
                'success' => true,
                'product_id' => $productId,
                'message' => 'Product added successfully',
                'product' => [
                    'id' => $productId,
                    'title' => $scrapedData['title'],
                    'platform' => $scrapedData['platform'],
                    'original_price' => $scrapedData['price'],
                    'current_price' => $scrapedData['price'],
                    'target_price' => $targetPrice,
                    'image_url' => $scrapedData['image_url']
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Add product error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to add product'];
        }
    }
    
    /**
     * Get user's products
     */
    public static function getUserProducts($userId, $limit = 50, $offset = 0) {
        try {
            $db = Database::getInstance();
            
            $stmt = $db->prepare("
                SELECT p.*, 
                       (SELECT price FROM price_history WHERE product_id = p.id ORDER BY created_at DESC LIMIT 1) as latest_price,
                       (SELECT COUNT(*) FROM price_history WHERE product_id = p.id) as price_history_count
                FROM products p 
                WHERE p.user_id = ? AND p.is_active = 1 
                ORDER BY p.created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$userId, $limit, $offset]);
            $products = $stmt->fetchAll();
            
            // Calculate savings and format data
            foreach ($products as &$product) {
                $currentPrice = $product['latest_price'] ?: $product['current_price'];
                $originalPrice = $product['original_price'];
                
                $product['current_price'] = $currentPrice;
                $product['savings'] = max(0, $originalPrice - $currentPrice);
                $product['savings_percentage'] = $originalPrice > 0 ? 
                    round((($originalPrice - $currentPrice) / $originalPrice) * 100, 2) : 0;
                $product['target_met'] = false;
                
                if ($product['target_price'] > 0) {
                    $product['target_met'] = $currentPrice <= $product['target_price'];
                } elseif ($product['target_percentage'] > 0) {
                    $targetPrice = $originalPrice * (1 - $product['target_percentage'] / 100);
                    $product['target_met'] = $currentPrice <= $targetPrice;
                }
            }
            
            return [
                'success' => true,
                'products' => $products
            ];
            
        } catch (Exception $e) {
            error_log("Get products error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to fetch products'];
        }
    }
    
    /**
     * Get product details with price history
     */
    public static function getProductDetails($productId, $userId) {
        try {
            $db = Database::getInstance();
            
            // Get product
            $stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND user_id = ?");
            $stmt->execute([$productId, $userId]);
            $product = $stmt->fetch();
            
            if (!$product) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            // Get price history
            $stmt = $db->prepare("
                SELECT price, created_at 
                FROM price_history 
                WHERE product_id = ? 
                ORDER BY created_at DESC 
                LIMIT 100
            ");
            $stmt->execute([$productId]);
            $priceHistory = $stmt->fetchAll();
            
            return [
                'success' => true,
                'product' => $product,
                'price_history' => $priceHistory
            ];
            
        } catch (Exception $e) {
            error_log("Get product details error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to fetch product details'];
        }
    }
    
    /**
     * Update product
     */
    public static function updateProduct($productId, $userId, $data) {
        try {
            $db = Database::getInstance();
            
            // Verify ownership
            $stmt = $db->prepare("SELECT id FROM products WHERE id = ? AND user_id = ?");
            $stmt->execute([$productId, $userId]);
            if (!$stmt->fetch()) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            $updates = [];
            $params = [];
            
            if (isset($data['target_price'])) {
                $updates[] = "target_price = ?";
                $params[] = floatval($data['target_price']);
            }
            
            if (isset($data['target_percentage'])) {
                $updates[] = "target_percentage = ?";
                $params[] = floatval($data['target_percentage']);
            }
            
            if (isset($data['is_active'])) {
                $updates[] = "is_active = ?";
                $params[] = $data['is_active'] ? 1 : 0;
            }
            
            if (!empty($updates)) {
                $params[] = $productId;
                $sql = "UPDATE products SET " . implode(', ', $updates) . ", updated_at = NOW() WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
            }
            
            return ['success' => true, 'message' => 'Product updated successfully'];
            
        } catch (Exception $e) {
            error_log("Update product error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to update product'];
        }
    }
    
    /**
     * Delete product
     */
    public static function deleteProduct($productId, $userId) {
        try {
            $db = Database::getInstance();
            
            // Verify ownership and delete
            $stmt = $db->prepare("DELETE FROM products WHERE id = ? AND user_id = ?");
            $stmt->execute([$productId, $userId]);
            
            if ($stmt->rowCount() === 0) {
                return ['success' => false, 'message' => 'Product not found'];
            }
            
            return ['success' => true, 'message' => 'Product deleted successfully'];
            
        } catch (Exception $e) {
            error_log("Delete product error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to delete product'];
        }
    }
    
    /**
     * Get user statistics
     */
    public static function getUserStatistics($userId) {
        try {
            $db = Database::getInstance();
            
            // Total products
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM products WHERE user_id = ? AND is_active = 1");
            $stmt->execute([$userId]);
            $totalProducts = $stmt->fetch()['count'];
            
            // Products with price drops
            $stmt = $db->prepare("
                SELECT COUNT(*) as count FROM products p
                WHERE p.user_id = ? AND p.is_active = 1 
                AND p.current_price < p.original_price
            ");
            $stmt->execute([$userId]);
            $productsWithDrops = $stmt->fetch()['count'];
            
            // Total savings
            $stmt = $db->prepare("
                SELECT SUM(p.original_price - p.current_price) as total_savings
                FROM products p
                WHERE p.user_id = ? AND p.is_active = 1 
                AND p.current_price < p.original_price
            ");
            $stmt->execute([$userId]);
            $totalSavings = $stmt->fetch()['total_savings'] ?: 0;
            
            // Products meeting target
            $stmt = $db->prepare("
                SELECT COUNT(*) as count FROM products p
                WHERE p.user_id = ? AND p.is_active = 1 
                AND (
                    (p.target_price > 0 AND p.current_price <= p.target_price) OR
                    (p.target_percentage > 0 AND p.current_price <= p.original_price * (1 - p.target_percentage / 100))
                )
            ");
            $stmt->execute([$userId]);
            $targetsMet = $stmt->fetch()['count'];
            
            return [
                'success' => true,
                'statistics' => [
                    'total_products' => $totalProducts,
                    'products_with_drops' => $productsWithDrops,
                    'total_savings' => round($totalSavings, 2),
                    'targets_met' => $targetsMet,
                    'average_discount' => $totalProducts > 0 ? 
                        round(($productsWithDrops / $totalProducts) * 100, 1) : 0
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Get statistics error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to fetch statistics'];
        }
    }
}
?>
