import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/models/product.dart';
import '../../../../core/services/api_service.dart';


class ProductService {
  static const String _boxName = 'products';
  late Box<Product> _productBox;
  final _uuid = const Uuid();
  final ApiService _apiService = ApiService.instance;


  Future<void> initialize() async {
    _productBox = await Hive.openBox<Product>(_boxName);
  }

  Future<List<Product>> getAllProducts() async {
    try {
      debugPrint('📦 Fetching products from API...');

      // Try to fetch from API first
      final response = await _apiService.getProducts();

      if (response['success'] == true) {
        final productsData = response['products'] as List;
        final products = productsData.map((data) => Product.fromJson(data)).toList();

        // Update local storage
        await _productBox.clear();
        for (final product in products) {
          await _productBox.put(product.id, product);
        }

        debugPrint('✅ Fetched ${products.length} products from API');
        return products;
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch products');
      }
    } catch (e) {
      debugPrint('⚠️ API fetch failed, using local storage: $e');
      // Fallback to local storage
      return _productBox.values.toList();
    }
  }

  Future<Product> addProduct(Product product) async {
    try {
      debugPrint('➕ Adding product to API: ${product.title}');

      // Add to API first
      final response = await _apiService.addProduct(product.toJson());

      if (response['success'] == true) {
        final apiProduct = Product.fromJson(response['product']);

        // Add to local storage
        await _productBox.put(apiProduct.id, apiProduct);

        debugPrint('✅ Product added successfully: ${apiProduct.title}');
        return apiProduct;
      } else {
        throw Exception(response['message'] ?? 'Failed to add product');
      }
    } catch (e) {
      debugPrint('⚠️ API add failed, saving locally: $e');
      // Fallback to local storage only
      await _productBox.put(product.id, product);
      return product;
    }
  }

  Future<Product> updateProduct(Product product) async {
    try {
      // Update local storage
      await _productBox.put(product.id, product);
      return product;
    } catch (e) {
      // Fallback to local storage only
      await _productBox.put(product.id, product);
      return product;
    }
  }

  Future<void> deleteProduct(String productId) async {
    try {
      // Delete from local storage
      await _productBox.delete(productId);
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  Future<Product?> getProduct(String productId) async {
    return _productBox.get(productId);
  }

  Future<Map<String, dynamic>> scrapeProductInfo(String url) async {
    try {
      debugPrint('🔍 Scraping product from URL: $url');

      final response = await _apiService.scrapeProduct({'url': url});

      if (response['success'] == true) {
        final productData = response['product'];
        debugPrint('✅ Product scraped successfully: ${productData['title']}');
        return productData;
      } else {
        throw Exception(response['error'] ?? 'Failed to scrape product');
      }
    } catch (e) {
      debugPrint('⚠️ Scraping failed, using fallback: $e');
      // Fallback to basic product info
      return {
        'title': 'Product from ${Uri.parse(url).host}',
        'price': 0.0,
        'image_url': '',
        'platform': Uri.parse(url).host,
      };
    }
  }

  Future<Product> createProductFromUrl(String url, double targetPrice, double targetPercentage) async {
    final scrapedData = await scrapeProductInfo(url);
    
    final product = Product(
      id: _uuid.v4(),
      title: scrapedData['title'] ?? 'Unknown Product',
      url: url,
      imageUrl: scrapedData['image_url'] ?? '',
      currentPrice: (scrapedData['current_price'] ?? 0.0).toDouble(),
      originalPrice: (scrapedData['original_price'] ?? scrapedData['current_price'] ?? 0.0).toDouble(),
      targetPrice: targetPrice,
      targetPercentage: targetPercentage,
      platform: scrapedData['platform'] ?? _detectPlatform(url),
      category: scrapedData['category'] ?? 'General',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isActive: true,
      priceHistory: [
        PriceHistory(
          price: (scrapedData['current_price'] ?? 0.0).toDouble(),
          timestamp: DateTime.now(),
        ),
      ],
      affiliateUrl: scrapedData['affiliate_url'],
    );

    return await addProduct(product);
  }

  String _detectPlatform(String url) {
    if (url.contains('amazon.')) return 'Amazon';
    if (url.contains('flipkart.')) return 'Flipkart';
    if (url.contains('myntra.')) return 'Myntra';
    if (url.contains('ajio.')) return 'Ajio';
    if (url.contains('nykaa.')) return 'Nykaa';
    return 'Other';
  }

  List<Product> filterProducts({
    String? platform,
    String? category,
    bool? hasReachedTarget,
    String? searchQuery,
  }) {
    var products = _productBox.values.toList();

    if (platform != null) {
      products = products.where((p) => p.platform == platform).toList();
    }

    if (category != null) {
      products = products.where((p) => p.category == category).toList();
    }

    if (hasReachedTarget != null) {
      products = products.where((p) => p.hasReachedTarget == hasReachedTarget).toList();
    }

    if (searchQuery != null && searchQuery.isNotEmpty) {
      products = products.where((p) => 
        p.title.toLowerCase().contains(searchQuery.toLowerCase())
      ).toList();
    }

    return products;
  }

  List<String> getAvailablePlatforms() {
    return _productBox.values
        .map((p) => p.platform)
        .toSet()
        .toList();
  }

  List<String> getAvailableCategories() {
    return _productBox.values
        .map((p) => p.category)
        .toSet()
        .toList();
  }

  Map<String, dynamic> getStatistics() {
    final products = _productBox.values.toList();
    final activeProducts = products.where((p) => p.isActive).toList();
    final reachedTargetProducts = products.where((p) => p.hasReachedTarget).toList();
    
    double totalSavings = 0;
    for (final product in reachedTargetProducts) {
      totalSavings += (product.originalPrice - product.currentPrice);
    }

    return {
      'total_products': products.length,
      'active_products': activeProducts.length,
      'reached_target': reachedTargetProducts.length,
      'total_savings': totalSavings,
      'average_discount': reachedTargetProducts.isNotEmpty 
          ? reachedTargetProducts.map((p) => p.discountPercentage).reduce((a, b) => a + b) / reachedTargetProducts.length
          : 0.0,
    };
  }
}