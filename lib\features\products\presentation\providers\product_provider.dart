import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/services/product_service.dart';
import '../../../../core/models/product.dart';

final productServiceProvider = Provider<ProductService>((ref) {
  return ProductService();
});

final productsProvider = StateNotifierProvider<ProductsNotifier, AsyncValue<List<Product>>>((ref) {
  final productService = ref.watch(productServiceProvider);
  return ProductsNotifier(productService);
});

final productFiltersProvider = StateProvider<ProductFilters>((ref) {
  return ProductFilters();
});

final filteredProductsProvider = Provider<AsyncValue<List<Product>>>((ref) {
  final products = ref.watch(productsProvider);
  final filters = ref.watch(productFiltersProvider);
  
  return products.when(
    data: (productList) {
      var filtered = productList;
      
      if (filters.platform != null) {
        filtered = filtered.where((p) => p.platform == filters.platform).toList();
      }
      
      if (filters.category != null) {
        filtered = filtered.where((p) => p.category == filters.category).toList();
      }
      
      if (filters.hasReachedTarget != null) {
        filtered = filtered.where((p) => p.hasReachedTarget == filters.hasReachedTarget).toList();
      }
      
      if (filters.searchQuery != null && filters.searchQuery!.isNotEmpty) {
        filtered = filtered.where((p) => 
          p.title.toLowerCase().contains(filters.searchQuery!.toLowerCase())
        ).toList();
      }
      
      // Sort by creation date (newest first)
      filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});



class ProductsNotifier extends StateNotifier<AsyncValue<List<Product>>> {
  final ProductService _productService;

  ProductsNotifier(this._productService) : super(const AsyncValue.loading()) {
    _loadProducts();
  }

  Future<void> _loadProducts() async {
    try {
      await _productService.initialize();
      final products = await _productService.getAllProducts();
      state = AsyncValue.data(products);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addProduct(Product product) async {
    try {
      final newProduct = await _productService.addProduct(product);
      state = state.when(
        data: (products) => AsyncValue.data([newProduct, ...products]),
        loading: () => AsyncValue.data([newProduct]),
        error: (_, __) => AsyncValue.data([newProduct]),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateProduct(Product product) async {
    try {
      final updatedProduct = await _productService.updateProduct(product);
      state = state.when(
        data: (products) {
          final index = products.indexWhere((p) => p.id == product.id);
          if (index != -1) {
            final updatedProducts = [...products];
            updatedProducts[index] = updatedProduct;
            return AsyncValue.data(updatedProducts);
          }
          return AsyncValue.data(products);
        },
        loading: () => state,
        error: (error, stack) => state,
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteProduct(String productId) async {
    try {
      await _productService.deleteProduct(productId);
      state = state.when(
        data: (products) => AsyncValue.data(
          products.where((p) => p.id != productId).toList(),
        ),
        loading: () => state,
        error: (error, stack) => state,
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<Product> createProductFromUrl(String url, double targetPrice, double targetPercentage) async {
    try {
      final product = await _productService.createProductFromUrl(url, targetPrice, targetPercentage);
      state = state.when(
        data: (products) => AsyncValue.data([product, ...products]),
        loading: () => AsyncValue.data([product]),
        error: (_, __) => AsyncValue.data([product]),
      );
      return product;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  void refresh() {
    _loadProducts();
  }
}

class ProductFilters {
  final String? platform;
  final String? category;
  final bool? hasReachedTarget;
  final String? searchQuery;

  ProductFilters({
    this.platform,
    this.category,
    this.hasReachedTarget,
    this.searchQuery,
  });

  ProductFilters copyWith({
    String? platform,
    String? category,
    bool? hasReachedTarget,
    String? searchQuery,
  }) {
    return ProductFilters(
      platform: platform ?? this.platform,
      category: category ?? this.category,
      hasReachedTarget: hasReachedTarget ?? this.hasReachedTarget,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

// Statistics provider
final productStatisticsProvider = Provider<Map<String, dynamic>>((ref) {
  final productsAsync = ref.watch(productsProvider);

  return productsAsync.when(
    data: (products) {
      final totalProducts = products.length;
      final activeProducts = products.where((p) => p.isActive).length;
      final reachedTarget = products.where((p) =>
        p.currentPrice <= p.targetPrice ||
        (p.targetPercentage > 0 && ((p.originalPrice - p.currentPrice) / p.originalPrice * 100) >= p.targetPercentage)
      ).length;

      final totalSavings = products.fold<double>(0, (sum, p) =>
        sum + (p.originalPrice - p.currentPrice).clamp(0, double.infinity));

      final avgDiscount = products.isNotEmpty
        ? products.fold<double>(0, (sum, p) {
            if (p.originalPrice <= 0) return sum; // Avoid division by zero
            final discount = ((p.originalPrice - p.currentPrice) / p.originalPrice * 100).clamp(0, 100);
            return sum + (discount.isFinite ? discount : 0.0); // Handle NaN/Infinity
          }) / products.length
        : 0.0;

      return {
        'total_products': totalProducts,
        'active_products': activeProducts,
        'reached_target': reachedTarget,
        'total_savings': totalSavings,
        'average_discount': avgDiscount,
      };
    },
    loading: () => {
      'total_products': 0,
      'active_products': 0,
      'reached_target': 0,
      'total_savings': 0.0,
      'average_discount': 0.0,
    },
    error: (_, __) => {
      'total_products': 0,
      'active_products': 0,
      'reached_target': 0,
      'total_savings': 0.0,
      'average_discount': 0.0,
    },
  );
});