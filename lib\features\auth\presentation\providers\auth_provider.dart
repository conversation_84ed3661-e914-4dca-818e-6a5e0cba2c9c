import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/models/user.dart';
import '../../domain/services/auth_service.dart';


final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

final authStateProvider = StreamProvider<User?>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.authStateChanges;
});

final authControllerProvider = StateNotifierProvider<AuthController, AsyncValue<User?>>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthController(authService);
});

class AuthController extends StateNotifier<AsyncValue<User?>> {
  final AuthService _authService;

  AuthController(this._authService) : super(const AsyncValue.loading()) {
    // Initialize with current user
    _initializeAuth();
  }

  void _initializeAuth() async {
    try {
      await for (final user in _authService.authStateChanges) {
        state = AsyncValue.data(user);
        break; // Just get the initial state
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> signInWithEmailAndPassword(String email, String password) async {
    state = const AsyncValue.loading();
    try {
      debugPrint('🔐 Controller: Starting login...');
      final user = await _authService.signInWithEmailAndPassword(email, password);
      state = AsyncValue.data(user);
      debugPrint('✅ Controller: Login successful');
    } catch (error, stackTrace) {
      debugPrint('❌ Controller: Login failed - $error');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> createUserWithEmailAndPassword(String email, String password, String displayName) async {
    state = const AsyncValue.loading();
    try {
      debugPrint('📝 Controller: Starting registration...');
      final user = await _authService.createUserWithEmailAndPassword(email, password, displayName);
      state = AsyncValue.data(user);
      debugPrint('✅ Controller: Registration successful');
    } catch (error, stackTrace) {
      debugPrint('❌ Controller: Registration failed - $error');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> signOut() async {
    try {
      debugPrint('👋 Controller: Starting sign out...');
      await _authService.signOut();
      state = const AsyncValue.data(null);
      debugPrint('✅ Controller: Sign out successful');
    } catch (error, stackTrace) {
      debugPrint('❌ Controller: Sign out failed - $error');
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
