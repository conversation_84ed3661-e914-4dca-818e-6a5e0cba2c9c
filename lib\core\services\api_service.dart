import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();
  
  ApiService._();
  
  final Dio _dio = Dio();
  static const String baseUrl = 'http://pricedropalert.mobunite.com/api_simple.php';
  
  // Initialize with interceptors
  void initialize() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add auth header if available
          final token = await _getSessionToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          
          debugPrint('🌐 API Request: ${options.method} ${options.path}');
          debugPrint('📤 Data: ${options.data}');
          
          handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint('📥 API Response: ${response.statusCode}');
          debugPrint('📄 Data: ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) {
          debugPrint('💥 API Error: ${error.message}');
          debugPrint('📄 Response: ${error.response?.data}');
          handler.next(error);
        },
      ),
    );
  }
  
  Future<String?> _getSessionToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('session_token');
  }
  
  // Product endpoints
  Future<Map<String, dynamic>> getProducts({int limit = 50, int offset = 0}) async {
    final response = await _dio.get(
      '$baseUrl?path=products',
      queryParameters: {'limit': limit, 'offset': offset},
    );
    return response.data;
  }
  
  Future<Map<String, dynamic>> addProduct(Map<String, dynamic> productData) async {
    final response = await _dio.post('$baseUrl?path=products', data: productData);
    return response.data;
  }
  
  Future<Map<String, dynamic>> getProduct(String productId) async {
    final response = await _dio.get('$baseUrl?path=products/$productId');
    return response.data;
  }
  
  Future<Map<String, dynamic>> updateProduct(String productId, Map<String, dynamic> data) async {
    final response = await _dio.put('$baseUrl?path=products/$productId', data: data);
    return response.data;
  }
  
  Future<Map<String, dynamic>> deleteProduct(String productId) async {
    final response = await _dio.delete('$baseUrl?path=products/$productId');
    return response.data;
  }
  
  Future<Map<String, dynamic>> scrapeProduct(Map<String, dynamic> urlData) async {
    final response = await _dio.post('$baseUrl?path=products/scrape', data: urlData);
    return response.data;
  }
  
  // User endpoints
  Future<Map<String, dynamic>> getUserProfile() async {
    final response = await _dio.get('$baseUrl?path=user/profile');
    return response.data;
  }
  
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> data) async {
    final response = await _dio.put('$baseUrl?path=user/profile', data: data);
    return response.data;
  }
  
  Future<Map<String, dynamic>> updateUserPreferences(Map<String, dynamic> data) async {
    final response = await _dio.put('$baseUrl?path=user/preferences', data: data);
    return response.data;
  }
  
  Future<Map<String, dynamic>> getUserStatistics() async {
    final response = await _dio.get('$baseUrl?path=user/statistics');
    return response.data;
  }
  
  // Utility endpoints
  Future<Map<String, dynamic>> getStatus() async {
    final response = await _dio.get('$baseUrl?path=status');
    return response.data;
  }
}
