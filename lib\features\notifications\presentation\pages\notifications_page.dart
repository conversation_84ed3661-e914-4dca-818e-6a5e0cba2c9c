import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/widgets/empty_state.dart';

// Mock notification model
class AppNotification {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? data;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.data,
  });
}

// Mock notifications provider
final notificationsProvider = StateNotifierProvider<NotificationsNotifier, List<AppNotification>>((ref) {
  return NotificationsNotifier();
});

class NotificationsNotifier extends StateNotifier<List<AppNotification>> {
  NotificationsNotifier() : super([]) {
    _loadMockNotifications();
  }

  void _loadMockNotifications() {
    // Mock notifications for demonstration
    state = [
      AppNotification(
        id: '1',
        title: '🎉 Price Drop Alert!',
        message: 'iPhone 15 Pro dropped by ₹5,000 (8% off)',
        type: 'price_drop',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        data: {'product_id': '123', 'old_price': 134900, 'new_price': 129900},
      ),
      AppNotification(
        id: '2',
        title: '🎯 Target Reached!',
        message: 'Samsung Galaxy S24 is now ₹65,000',
        type: 'target_reached',
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
        data: {'product_id': '456', 'target_price': 65000},
      ),
      AppNotification(
        id: '3',
        title: '📦 Back in Stock!',
        message: 'MacBook Air M3 is now available',
        type: 'stock_alert',
        timestamp: DateTime.now().subtract(const Duration(days: 2)),
        isRead: true,
        data: {'product_id': '789'},
      ),
      AppNotification(
        id: '4',
        title: '⚠️ Price Increase',
        message: 'AirPods Pro price increased by ₹2,000',
        type: 'price_increase',
        timestamp: DateTime.now().subtract(const Duration(days: 3)),
        isRead: true,
        data: {'product_id': '101', 'old_price': 24900, 'new_price': 26900},
      ),
    ];
  }

  void markAsRead(String notificationId) {
    state = state.map((notification) {
      if (notification.id == notificationId) {
        return AppNotification(
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          timestamp: notification.timestamp,
          isRead: true,
          data: notification.data,
        );
      }
      return notification;
    }).toList();
  }

  void markAllAsRead() {
    state = state.map((notification) => AppNotification(
      id: notification.id,
      title: notification.title,
      message: notification.message,
      type: notification.type,
      timestamp: notification.timestamp,
      isRead: true,
      data: notification.data,
    )).toList();
  }

  void deleteNotification(String notificationId) {
    state = state.where((notification) => notification.id != notificationId).toList();
  }

  void clearAll() {
    state = [];
  }
}

class NotificationsPage extends ConsumerWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifications = ref.watch(notificationsProvider);
    final unreadCount = notifications.where((n) => !n.isRead).length;

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Notifications'),
            if (unreadCount > 0)
              Text(
                '$unreadCount unread',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.normal,
                ),
              ),
          ],
        ),
        actions: [
          if (notifications.isNotEmpty) ...[
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'mark_all_read':
                    ref.read(notificationsProvider.notifier).markAllAsRead();
                    break;
                  case 'clear_all':
                    _showClearAllDialog(context, ref);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'mark_all_read',
                  child: Row(
                    children: [
                      Icon(Icons.mark_email_read),
                      SizedBox(width: 8),
                      Text('Mark all as read'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'clear_all',
                  child: Row(
                    children: [
                      Icon(Icons.clear_all),
                      SizedBox(width: 8),
                      Text('Clear all'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
      body: notifications.isEmpty
          ? const EmptyState(
              icon: Icons.notifications_none,
              title: 'No Notifications',
              subtitle: 'You\'ll see price alerts and updates here',
            )
          : ListView.builder(
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return NotificationTile(
                  notification: notification,
                  onTap: () {
                    if (!notification.isRead) {
                      ref.read(notificationsProvider.notifier).markAsRead(notification.id);
                    }
                    _handleNotificationTap(context, notification);
                  },
                  onDismiss: () {
                    ref.read(notificationsProvider.notifier).deleteNotification(notification.id);
                  },
                );
              },
            ),
    );
  }

  void _showClearAllDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(notificationsProvider.notifier).clearAll();
              Navigator.of(context).pop();
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _handleNotificationTap(BuildContext context, AppNotification notification) {
    // Handle different notification types
    switch (notification.type) {
      case 'price_drop':
      case 'target_reached':
      case 'stock_alert':
      case 'price_increase':
        // Navigate to product details if product_id is available
        if (notification.data?['product_id'] != null) {
          // TODO: Navigate to product details page
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Opening product ${notification.data!['product_id']}'),
            ),
          );
        }
        break;
      default:
        // Handle other notification types
        break;
    }
  }
}

class NotificationTile extends StatelessWidget {
  final AppNotification notification;
  final VoidCallback onTap;
  final VoidCallback onDismiss;

  const NotificationTile({
    super.key,
    required this.notification,
    required this.onTap,
    required this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      onDismissed: (_) => onDismiss(),
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: Colors.red,
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: notification.isRead ? Colors.grey[300] : Theme.of(context).primaryColor,
          child: Text(
            _getNotificationIcon(notification.type),
            style: TextStyle(
              color: notification.isRead ? Colors.grey[600] : Colors.white,
            ),
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.message,
              style: TextStyle(
                color: notification.isRead ? Colors.grey[600] : null,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(notification.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: notification.isRead
            ? null
            : Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                ),
              ),
        onTap: onTap,
      ),
    );
  }

  String _getNotificationIcon(String type) {
    switch (type) {
      case 'price_drop':
        return '🎉';
      case 'target_reached':
        return '🎯';
      case 'stock_alert':
        return '📦';
      case 'price_increase':
        return '⚠️';
      default:
        return '📱';
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d, y').format(timestamp);
    }
  }
}
