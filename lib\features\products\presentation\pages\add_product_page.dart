import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart' as mobile_scanner;
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import '../providers/product_provider.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_button.dart';

class AddProductPage extends ConsumerStatefulWidget {
  const AddProductPage({super.key});

  @override
  ConsumerState<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends ConsumerState<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _urlController = TextEditingController();
  final _targetPriceController = TextEditingController();
  final _targetPercentageController = TextEditingController();
  
  bool _isLoading = false;
  bool _usePercentage = false;

  @override
  void initState() {
    super.initState();
    _checkForSharedUrl();
  }

  @override
  void dispose() {
    _urlController.dispose();
    _targetPriceController.dispose();
    _targetPercentageController.dispose();
    super.dispose();
  }

  void _checkForSharedUrl() {
    // Check for shared URL from other apps
    ReceiveSharingIntent.instance.getMediaStream().listen((List<SharedMediaFile> files) {
      for (final file in files) {
        if (file.type == SharedMediaType.text && _isValidUrl(file.path)) {
          setState(() {
            _urlController.text = file.path;
          });
          break;
        }
      }
    });

    // Check for initial shared URL
    ReceiveSharingIntent.instance.getInitialMedia().then((List<SharedMediaFile> files) {
      for (final file in files) {
        if (file.type == SharedMediaType.text && _isValidUrl(file.path)) {
          setState(() {
            _urlController.text = file.path;
          });
          break;
        }
      }
    });
  }

  bool _isValidUrl(String url) {
    return Uri.tryParse(url) != null && 
           (url.contains('amazon.') || 
            url.contains('flipkart.') || 
            url.contains('myntra.') ||
            url.contains('ajio.') ||
            url.contains('nykaa.'));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Product'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // URL Input Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Product URL',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Enter the product URL from Amazon, Flipkart, Myntra, Ajio, or Nykaa',
                        style: TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      
                      CustomTextField(
                        controller: _urlController,
                        label: 'Product URL',
                        hint: 'https://www.amazon.in/product...',
                        prefixIcon: Icons.link,
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a product URL';
                          }
                          if (!_isValidUrl(value)) {
                            return 'Please enter a valid product URL from supported platforms';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Quick Actions
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: _pasteFromClipboard,
                              icon: const Icon(Icons.paste),
                              label: const Text('Paste'),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: _scanQRCode,
                              icon: const Icon(Icons.qr_code_scanner),
                              label: const Text('Scan QR'),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: _scanBarcode,
                              icon: const Icon(Icons.qr_code),
                              label: const Text('Barcode'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Target Settings Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Price Alert Settings',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Set your target price or discount percentage',
                        style: TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      
                      // Toggle between price and percentage
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<String>(
                              title: const Text('Target Price'),
                              value: 'price',
                              groupValue: _usePercentage ? 'percentage' : 'price',
                              onChanged: (value) {
                                setState(() {
                                  _usePercentage = value == 'percentage';
                                  if (_usePercentage) {
                                    _targetPriceController.clear();
                                  } else {
                                    _targetPercentageController.clear();
                                  }
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<String>(
                              title: const Text('Discount %'),
                              value: 'percentage',
                              groupValue: _usePercentage ? 'percentage' : 'price',
                              onChanged: (value) {
                                setState(() {
                                  _usePercentage = value == 'percentage';
                                  if (_usePercentage) {
                                    _targetPriceController.clear();
                                  } else {
                                    _targetPercentageController.clear();
                                  }
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      if (!_usePercentage)
                        CustomTextField(
                          controller: _targetPriceController,
                          label: 'Target Price (₹)',
                          hint: '1000',
                          prefixIcon: Icons.currency_rupee,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (!_usePercentage && (value == null || value.isEmpty)) {
                              return 'Please enter target price';
                            }
                            if (!_usePercentage && double.tryParse(value!) == null) {
                              return 'Please enter a valid price';
                            }
                            return null;
                          },
                        )
                      else
                        CustomTextField(
                          controller: _targetPercentageController,
                          label: 'Target Discount (%)',
                          hint: '20',
                          prefixIcon: Icons.percent,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (_usePercentage && (value == null || value.isEmpty)) {
                              return 'Please enter target discount percentage';
                            }
                            if (_usePercentage) {
                              final percentage = double.tryParse(value!);
                              if (percentage == null || percentage < 0 || percentage > 100) {
                                return 'Please enter a valid percentage (0-100)';
                              }
                            }
                            return null;
                          },
                        ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Add Product Button
              LoadingButton(
                onPressed: _addProduct,
                isLoading: _isLoading,
                child: const Text('Start Tracking'),
              ),
              
              const SizedBox(height: 16),
              
              // Supported Platforms Info
              Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue[700]),
                          const SizedBox(width: 8),
                          Text(
                            'Supported Platforms',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '• Amazon India\n• Flipkart\n• Myntra\n• Ajio\n• Nykaa',
                        style: TextStyle(color: Colors.black87),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pasteFromClipboard() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData?.text != null && _isValidUrl(clipboardData!.text!)) {
      setState(() {
        _urlController.text = clipboardData.text!;
      });
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No valid product URL found in clipboard'),
          ),
        );
      }
    }
  }

  Future<void> _scanQRCode() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const QRScannerPage(),
      ),
    );
    
    if (result != null && _isValidUrl(result)) {
      setState(() {
        _urlController.text = result;
      });
    }
  }

  Future<void> _scanBarcode() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BarcodeScannerPage(),
      ),
    );
    
    if (result != null && mounted) {
      // Convert barcode to search URL (implementation depends on requirements)
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Scanned barcode: $result')),
      );
    }
  }

  Future<void> _addProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final targetPrice = _usePercentage ? 0.0 : double.parse(_targetPriceController.text);
      final targetPercentage = _usePercentage ? double.parse(_targetPercentageController.text) : 0.0;

      await ref.read(productsProvider.notifier).createProductFromUrl(
        _urlController.text.trim(),
        targetPrice,
        targetPercentage,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Product added successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        context.go('/home');
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

class QRScannerPage extends StatefulWidget {
  const QRScannerPage({super.key});

  @override
  State<QRScannerPage> createState() => _QRScannerPageState();
}

class _QRScannerPageState extends State<QRScannerPage> {
  // No controller needed for mobile_scanner

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan QR Code'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: mobile_scanner.MobileScanner(
        onDetect: (capture) {
          final List<mobile_scanner.Barcode> barcodes = capture.barcodes;
          for (final barcode in barcodes) {
            if (barcode.rawValue != null && mounted) {
              Navigator.of(context).pop(barcode.rawValue);
              break;
            }
          }
        },
      ),
    );
  }


}

class BarcodeScannerPage extends StatefulWidget {
  const BarcodeScannerPage({super.key});

  @override
  State<BarcodeScannerPage> createState() => _BarcodeScannerPageState();
}

class _BarcodeScannerPageState extends State<BarcodeScannerPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Barcode'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: mobile_scanner.MobileScanner(
        onDetect: (capture) {
          final List<mobile_scanner.Barcode> barcodes = capture.barcodes;
          for (final barcode in barcodes) {
            if (barcode.rawValue != null) {
              Navigator.of(context).pop(barcode.rawValue);
              break;
            }
          }
        },
      ),
    );
  }
}