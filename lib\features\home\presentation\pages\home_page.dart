import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../products/presentation/providers/product_provider.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../../../notifications/presentation/pages/notifications_page.dart';
import '../../../../core/widgets/product_card.dart';
import '../../../../core/widgets/statistics_card.dart';
import '../../../../core/widgets/empty_state.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(authStateProvider).value;
    final products = ref.watch(filteredProductsProvider);
    final statistics = ref.watch(productStatisticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Hello, ${user?.displayName?.split(' ').first ?? 'User'}!',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
            ),
            const Text(
              'PriceDrop Alert',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () => _showNotifications(context),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilters(context),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.read(productsProvider.notifier).refresh();
        },
        child: CustomScrollView(
          slivers: [
            // Statistics Section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Search Bar
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search products...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  ref.read(productFiltersProvider.notifier).update(
                                    (state) => state.copyWith(searchQuery: ''),
                                  );
                                },
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onChanged: (value) {
                        ref.read(productFiltersProvider.notifier).update(
                          (state) => state.copyWith(searchQuery: value),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Statistics Cards
                    Row(
                      children: [
                        Expanded(
                          child: StatisticsCard(
                            title: 'Tracking',
                            value: statistics['active_products'].toString(),
                            subtitle: 'Products',
                            icon: Icons.track_changes,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: StatisticsCard(
                            title: 'Target Hit',
                            value: statistics['reached_target'].toString(),
                            subtitle: 'Products',
                            icon: Icons.flag,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    Row(
                      children: [
                        Expanded(
                          child: StatisticsCard(
                            title: 'Total Savings',
                            value: '₹${NumberFormat('#,##0').format(statistics['total_savings'])}',
                            subtitle: 'Saved',
                            icon: Icons.savings,
                            color: Colors.orange,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: StatisticsCard(
                            title: 'Avg Discount',
                            value: '${statistics['average_discount'].toStringAsFixed(1)}%',
                            subtitle: 'Discount',
                            icon: Icons.percent,
                            color: Colors.purple,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Section Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Your Products',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton.icon(
                          onPressed: () => context.go('/add-product'),
                          icon: const Icon(Icons.add),
                          label: const Text('Add Product'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Products List
            products.when(
              data: (productList) {
                if (productList.isEmpty) {
                  return const SliverFillRemaining(
                    child: EmptyState(
                      icon: Icons.shopping_cart_outlined,
                      title: 'No Products Yet',
                      subtitle: 'Start tracking your favorite products to get price drop alerts',
                      actionText: 'Add Your First Product',
                    ),
                  );
                }
                
                return SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final product = productList[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: ProductCard(
                            product: product,
                            onTap: () => context.go('/product/${product.id}'),
                            onDelete: () => _deleteProduct(product.id),
                          ),
                        );
                      },
                      childCount: productList.length,
                    ),
                  ),
                );
              },
              loading: () => const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, _) => SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error: $error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => ref.read(productsProvider.notifier).refresh(),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/add-product'),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const NotificationsPage(),
      ),
    );
  }

  void _showFilters(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const FilterBottomSheet(),
    );
  }

  void _deleteProduct(String productId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: const Text('Are you sure you want to stop tracking this product?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(productsProvider.notifier).deleteProduct(productId);
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class FilterBottomSheet extends ConsumerWidget {
  const FilterBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filters = ref.watch(productFiltersProvider);
    final productService = ref.watch(productServiceProvider);

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filter Products',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          
          // Platform Filter
          const Text('Platform', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: productService.getAvailablePlatforms().map((platform) {
              final isSelected = filters.platform == platform;
              return FilterChip(
                label: Text(platform),
                selected: isSelected,
                onSelected: (selected) {
                  ref.read(productFiltersProvider.notifier).update(
                    (state) => state.copyWith(
                      platform: selected ? platform : null,
                    ),
                  );
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Status Filter
          const Text('Status', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('Target Reached'),
                selected: filters.hasReachedTarget == true,
                onSelected: (selected) {
                  ref.read(productFiltersProvider.notifier).update(
                    (state) => state.copyWith(
                      hasReachedTarget: selected ? true : null,
                    ),
                  );
                },
              ),
              FilterChip(
                label: const Text('Tracking'),
                selected: filters.hasReachedTarget == false,
                onSelected: (selected) {
                  ref.read(productFiltersProvider.notifier).update(
                    (state) => state.copyWith(
                      hasReachedTarget: selected ? false : null,
                    ),
                  );
                },
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    ref.read(productFiltersProvider.notifier).update(
                      (state) => ProductFilters(),
                    );
                  },
                  child: const Text('Clear All'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}