import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'user.g.dart';

@HiveType(typeId: 2)
@JsonSerializable()
class User extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String email;
  
  @HiveField(2)
  final String? displayName;
  
  @HiveField(3)
  final String? photoUrl;
  
  @HiveField(4)
  final DateTime createdAt;
  
  @HiveField(5)
  final DateTime lastLoginAt;
  
  @HiveField(6)
  final UserPreferences preferences;

  User({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    required this.createdAt,
    required this.lastLoginAt,
    required this.preferences,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}

@HiveType(typeId: 3)
@JsonSerializable()
class UserPreferences extends HiveObject {
  @HiveField(0)
  final bool notificationsEnabled;
  
  @HiveField(1)
  final int checkFrequencyHours;
  
  @HiveField(2)
  final bool darkMode;
  
  @HiveField(3)
  final String currency;
  
  @HiveField(4)
  final List<String> preferredPlatforms;

  UserPreferences({
    this.notificationsEnabled = true,
    this.checkFrequencyHours = 24,
    this.darkMode = false,
    this.currency = 'INR',
    this.preferredPlatforms = const ['Amazon', 'Flipkart'],
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) => _$UserPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);

  UserPreferences copyWith({
    bool? notificationsEnabled,
    int? checkFrequencyHours,
    bool? darkMode,
    String? currency,
    List<String>? preferredPlatforms,
  }) {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      checkFrequencyHours: checkFrequencyHours ?? this.checkFrequencyHours,
      darkMode: darkMode ?? this.darkMode,
      currency: currency ?? this.currency,
      preferredPlatforms: preferredPlatforms ?? this.preferredPlatforms,
    );
  }
}