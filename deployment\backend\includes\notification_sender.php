<?php
/**
 * Notification Sender Class
 * Handles FCM push notifications and email notifications
 */

// Include required files if not already loaded
if (!class_exists('Database')) {
    require_once __DIR__ . '/../config/database.php';
}

class NotificationSender {
    private $pdo;
    
    public function __construct() {
        $this->pdo = Database::getInstance();
    }
    
    public function sendTargetReachedNotification($userId, $product, $currentPrice) {
        $title = "🎯 Target Reached!";
        $message = "{$product['title']} is now ₹{$currentPrice}";
        
        $this->sendPushNotification($userId, $title, $message, [
            'type' => 'target_reached',
            'product_id' => $product['id'],
            'current_price' => $currentPrice,
            'product_url' => $product['url']
        ]);
        
        $this->sendEmailNotification($userId, $title, $this->getTargetReachedEmailBody($product, $currentPrice));
    }
    
    public function sendPriceDropNotification($userId, $product, $currentPrice, $previousPrice) {
        $savings = $previousPrice - $currentPrice;
        $title = "📉 Price Drop Alert!";
        $message = "{$product['title']} dropped by ₹{$savings}";
        
        $this->sendPushNotification($userId, $title, $message, [
            'type' => 'price_drop',
            'product_id' => $product['id'],
            'current_price' => $currentPrice,
            'previous_price' => $previousPrice,
            'savings' => $savings,
            'product_url' => $product['url']
        ]);
        
        $this->sendEmailNotification($userId, $title, $this->getPriceDropEmailBody($product, $currentPrice, $previousPrice));
    }
    
    private function sendPushNotification($userId, $title, $message, $data = []) {
        try {
            // Get user's FCM tokens
            $tokens = $this->getUserFCMTokens($userId);
            
            if (empty($tokens)) {
                echo "  No FCM tokens found for user {$userId}\n";
                return false;
            }
            
            $payload = [
                'registration_ids' => $tokens,
                'notification' => [
                    'title' => $title,
                    'body' => $message,
                    'icon' => 'ic_notification',
                    'sound' => 'default',
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                ],
                'data' => array_merge($data, [
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                    'timestamp' => time()
                ])
            ];
            
            $result = $this->sendFCMRequest($payload);
            
            if ($result['success']) {
                echo "  ✅ Push notification sent successfully\n";
                return true;
            } else {
                echo "  ❌ Failed to send push notification: {$result['error']}\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "  ❌ Push notification error: " . $e->getMessage() . "\n";
            error_log("FCM notification error: " . $e->getMessage());
            return false;
        }
    }
    
    private function sendEmailNotification($userId, $subject, $body) {
        try {
            // Get user email preferences
            $user = $this->getUserEmailPreferences($userId);
            
            if (!$user || !$user['email_notifications']) {
                echo "  Email notifications disabled for user {$userId}\n";
                return false;
            }
            
            // Simple email sending (you can enhance this with PHPMailer)
            $headers = [
                'From: ' . SMTP_FROM_NAME . ' <' . SMTP_FROM_EMAIL . '>',
                'Reply-To: ' . SMTP_FROM_EMAIL,
                'Content-Type: text/html; charset=UTF-8',
                'X-Mailer: PriceDrop Alert'
            ];
            
            $success = mail($user['email'], $subject, $body, implode("\r\n", $headers));
            
            if ($success) {
                echo "  ✅ Email notification sent to {$user['email']}\n";
                return true;
            } else {
                echo "  ❌ Failed to send email to {$user['email']}\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "  ❌ Email notification error: " . $e->getMessage() . "\n";
            error_log("Email notification error: " . $e->getMessage());
            return false;
        }
    }
    
    private function getUserFCMTokens($userId) {
        $stmt = $this->pdo->prepare("
            SELECT token 
            FROM fcm_tokens 
            WHERE user_id = ? AND is_active = 1
        ");
        $stmt->execute([$userId]);
        
        $tokens = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $tokens[] = $row['token'];
        }
        
        return $tokens;
    }
    
    private function getUserEmailPreferences($userId) {
        $stmt = $this->pdo->prepare("
            SELECT u.email, u.display_name, 
                   COALESCE(up.email_notifications, 1) as email_notifications
            FROM users u
            LEFT JOIN user_preferences up ON u.id = up.user_id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function sendFCMRequest($payload) {
        if (!defined('FCM_SERVER_KEY') || FCM_SERVER_KEY === 'your-fcm-server-key') {
            return ['success' => false, 'error' => 'FCM server key not configured'];
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: key=' . FCM_SERVER_KEY,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if ($result && $result['success'] > 0) {
                return ['success' => true];
            } else {
                return ['success' => false, 'error' => 'FCM request failed: ' . $response];
            }
        } else {
            return ['success' => false, 'error' => "HTTP {$httpCode}: {$response}"];
        }
    }
    
    private function getTargetReachedEmailBody($product, $currentPrice) {
        return "
        <html>
        <body style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #4CAF50; color: white; padding: 20px; text-align: center;'>
                <h1>🎯 Target Reached!</h1>
            </div>
            <div style='padding: 20px;'>
                <h2>{$product['title']}</h2>
                <p>Great news! The product you're tracking has reached your target price.</p>
                <div style='background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p><strong>Current Price:</strong> ₹{$currentPrice}</p>
                    <p><strong>Your Target:</strong> ₹{$product['target_price']}</p>
                </div>
                <p style='text-align: center;'>
                    <a href='{$product['url']}' style='background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                        Buy Now
                    </a>
                </p>
                <p style='color: #666; font-size: 12px; margin-top: 30px;'>
                    This notification was sent by PriceDrop Alert. You can manage your notifications in the app settings.
                </p>
            </div>
        </body>
        </html>";
    }
    
    private function getPriceDropEmailBody($product, $currentPrice, $previousPrice) {
        $savings = $previousPrice - $currentPrice;
        $discountPercent = round((($previousPrice - $currentPrice) / $previousPrice) * 100, 1);
        
        return "
        <html>
        <body style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #2196F3; color: white; padding: 20px; text-align: center;'>
                <h1>📉 Price Drop Alert!</h1>
            </div>
            <div style='padding: 20px;'>
                <h2>{$product['title']}</h2>
                <p>The price has dropped for a product you're tracking!</p>
                <div style='background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p><strong>Previous Price:</strong> <span style='text-decoration: line-through;'>₹{$previousPrice}</span></p>
                    <p><strong>Current Price:</strong> <span style='color: #4CAF50; font-size: 18px;'>₹{$currentPrice}</span></p>
                    <p><strong>You Save:</strong> <span style='color: #4CAF50;'>₹{$savings} ({$discountPercent}% off)</span></p>
                </div>
                <p style='text-align: center;'>
                    <a href='{$product['url']}' style='background: #2196F3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                        Buy Now
                    </a>
                </p>
                <p style='color: #666; font-size: 12px; margin-top: 30px;'>
                    This notification was sent by PriceDrop Alert. You can manage your notifications in the app settings.
                </p>
            </div>
        </body>
        </html>";
    }
}
?>
